"use client"

import dynamic from 'next/dynamic'
import { Terminal } from '@xterm/xterm'
import { useState } from 'react'

// ✅ Fix SSR error: Dynamic import with no SSR for browser-only xterm.js
const TerminalBootstrap = dynamic(
  () => import('@/components/terminal').then(mod => ({ default: mod.TerminalBootstrap })),
  {
    ssr: false,
    loading: () => (
      <div className="h-full flex items-center justify-center bg-black text-white">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white mx-auto mb-2"></div>
          <p>Loading Terminal...</p>
        </div>
      </div>
    )
  }
)

export default function TerminalTestPage() {
  const [terminalInstance, setTerminalInstance] = useState<Terminal | null>(null)

  const handleTerminalReady = (terminal: Terminal) => {
    setTerminalInstance(terminal)
    console.log('✅ Terminal ready in test page:', terminal)
  }

  return (
    <div className="h-screen flex flex-col bg-background">
      <div className="h-12 border-b border-border flex items-center px-4 bg-background">
        <h1 className="text-lg font-semibold">Terminal Bootstrap Test</h1>
        <div className="ml-auto text-sm text-muted-foreground">
          Status: {terminalInstance ? '🟢 Ready' : '🟡 Initializing...'}
        </div>
      </div>
      
      <div className="flex-1 p-4">
        <div className="h-full border border-border rounded-lg overflow-hidden">
          <TerminalBootstrap 
            className="h-full"
            onReady={handleTerminalReady}
          />
        </div>
      </div>
      
      <div className="h-8 border-t border-border bg-muted/50 flex items-center px-4 text-xs text-muted-foreground">
        Terminal Bootstrap Test - Phase 1 Verification
      </div>
    </div>
  )
}
