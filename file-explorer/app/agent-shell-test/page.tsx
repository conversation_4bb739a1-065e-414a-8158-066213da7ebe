"use client"

import { useState } from 'react'
import dynamic from 'next/dynamic'
import { InternAgent } from '@/components/agents/implementation/intern-agent'
import { terminalEventBus } from '@/components/terminal/terminal-event-bus'

// ✅ Fix SSR error: Dynamic import with no SSR for browser-only xterm.js
const TerminalBootstrap = dynamic(
  () => import('@/components/terminal').then(mod => ({ default: mod.TerminalBootstrap })),
  {
    ssr: false,
    loading: () => (
      <div className="h-full flex items-center justify-center bg-black text-white">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white mx-auto mb-2"></div>
          <p>Loading Terminal...</p>
        </div>
      </div>
    )
  }
)

export default function AgentShellTestPage() {
  const [testResults, setTestResults] = useState<string[]>([])
  const [isRunning, setIsRunning] = useState(false)
  const [customCommand, setCustomCommand] = useState('')

  const addResult = (message: string) => {
    setTestResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`])
  }

  const runAgentShellTests = async () => {
    setIsRunning(true)
    setTestResults([])
    
    try {
      addResult('🚀 Starting Agent Shell Integration Tests...')
      
      // Create InternAgent instance
      const internAgent = new InternAgent({
        id: 'test-intern',
        name: 'Test Intern Agent',
        provider: 'openai',
        model: 'gpt-4',
        temperature: 0.7,
        maxTokens: 2000
      })

      addResult('✅ InternAgent created successfully')

      // Test individual commands
      const testCommands = [
        'pwd',
        'ls -la',
        'echo "Hello from Agent Shell Test"',
        'whoami',
        'date'
      ]

      for (const command of testCommands) {
        addResult(`🔄 Testing command: ${command}`)
        try {
          const result = await internAgent.executeShellCommand(command)
          if (result.success) {
            addResult(`✅ Command "${command}" succeeded`)
            addResult(`   Output: ${result.output.substring(0, 200)}${result.output.length > 200 ? '...' : ''}`)
          } else {
            addResult(`❌ Command "${command}" failed: ${result.error}`)
          }
        } catch (error) {
          addResult(`💥 Command "${command}" threw error: ${error}`)
        }
      }

      // Test security constraints
      addResult('🔒 Testing security constraints...')
      const dangerousCommands = [
        'rm -rf /',
        'sudo rm -rf',
        'shutdown',
        'reboot'
      ]

      for (const command of dangerousCommands) {
        addResult(`🔄 Testing dangerous command: ${command}`)
        try {
          const result = await internAgent.executeShellCommand(command)
          if (result.success) {
            addResult(`⚠️ SECURITY ISSUE: Dangerous command "${command}" was allowed!`)
          } else {
            addResult(`✅ Security: Command "${command}" correctly blocked`)
            addResult(`   Reason: ${result.error}`)
          }
        } catch (error) {
          addResult(`💥 Dangerous command "${command}" threw error: ${error}`)
        }
      }

      addResult('🎉 Agent Shell Integration Tests completed!')

    } catch (error) {
      addResult(`💥 Test suite failed: ${error}`)
    } finally {
      setIsRunning(false)
    }
  }

  const runCustomCommand = async () => {
    if (!customCommand.trim()) return

    setIsRunning(true)
    try {
      addResult(`🔄 Running custom command: ${customCommand}`)
      
      const internAgent = new InternAgent({
        id: 'test-intern-custom',
        name: 'Test Intern Agent',
        provider: 'openai',
        model: 'gpt-4',
        temperature: 0.7,
        maxTokens: 2000
      })

      const result = await internAgent.executeShellCommand(customCommand)
      
      if (result.success) {
        addResult(`✅ Custom command succeeded`)
        addResult(`   Output: ${result.output}`)
      } else {
        addResult(`❌ Custom command failed: ${result.error}`)
      }
    } catch (error) {
      addResult(`💥 Custom command threw error: ${error}`)
    } finally {
      setIsRunning(false)
    }
  }

  const clearTerminal = () => {
    terminalEventBus.emitClearTerminal()
    setTestResults([])
  }

  return (
    <div className="h-screen flex flex-col bg-background">
      <div className="h-12 border-b border-border flex items-center px-4 bg-background">
        <h1 className="text-lg font-semibold">Agent Shell Integration Test - Terminal UI Streaming</h1>
        <div className="ml-auto text-sm text-muted-foreground">
          Status: {isRunning ? '🟡 Running...' : '🟢 Ready'}
        </div>
      </div>

      <div className="flex-1 flex">
        {/* Controls */}
        <div className="w-80 border-r border-border p-4 space-y-4">
          <div>
            <button
              onClick={runAgentShellTests}
              disabled={isRunning}
              className="w-full px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isRunning ? 'Running Tests...' : 'Run Agent Shell Tests'}
            </button>
          </div>

          <div className="space-y-2">
            <label className="block text-sm font-medium">Custom Command:</label>
            <input
              type="text"
              value={customCommand}
              onChange={(e) => setCustomCommand(e.target.value)}
              placeholder="e.g., ls -la, pwd, echo hello"
              className="w-full px-3 py-2 border border-border rounded-md bg-background"
              disabled={isRunning}
            />
            <button
              onClick={runCustomCommand}
              disabled={isRunning || !customCommand.trim()}
              className="w-full px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Run Custom Command
            </button>
          </div>

          <div>
            <button
              onClick={clearTerminal}
              disabled={isRunning}
              className="w-full px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Clear Terminal
            </button>
          </div>

          <div className="text-xs text-muted-foreground">
            <p><strong>Safe Commands:</strong></p>
            <p>• pwd, ls, echo, whoami, date</p>
            <p>• cat (small files), head, tail</p>
            <br />
            <p><strong>Blocked Commands:</strong></p>
            <p>• rm -rf, sudo, shutdown</p>
            <p>• reboot, mkfs, fdisk</p>
          </div>
        </div>

        {/* Terminal */}
        <div className="flex-1 flex flex-col">
          <div className="h-8 border-b border-border bg-muted/50 flex items-center px-4 text-xs text-muted-foreground">
            Live Terminal - Agent Output Streaming
          </div>
          <div className="flex-1 p-4">
            <div className="h-full border border-border rounded-lg overflow-hidden">
              <TerminalBootstrap className="h-full" />
            </div>
          </div>

          {/* Console Log Results */}
          <div className="h-32 border-t border-border bg-muted/20 p-2 overflow-auto">
            <div className="text-xs text-muted-foreground mb-1">Console Log Results:</div>
            <div className="text-xs font-mono">
              {testResults.length === 0 ? (
                <div className="text-gray-500">No test results yet...</div>
              ) : (
                testResults.map((result, index) => (
                  <div key={index} className="mb-1">
                    {result}
                  </div>
                ))
              )}
            </div>
          </div>
        </div>
      </div>
      
      <div className="h-8 border-t border-border bg-muted/50 flex items-center px-4 text-xs text-muted-foreground">
        Task 92 - Agent Shell Output Streaming to Terminal UI
      </div>
    </div>
  )
}
