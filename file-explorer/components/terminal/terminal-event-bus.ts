// components/terminal/terminal-event-bus.ts

export interface TerminalAgentOutput {
  output: string;
  agentId: string;
  agentName?: string;
  command?: string;
  success?: boolean;
  timestamp: number;
}

export interface TerminalSystemMessage {
  message: string;
  type: 'info' | 'warning' | 'error' | 'success';
  timestamp: number;
}

// ✅ Task 94: Manual command routing interfaces
export interface TerminalManualCommand {
  input: string;
  agentId: string;
  timestamp: number;
}

export interface TerminalAgentResponse {
  output: string;
  agentId: string;
  success?: boolean;
  status?: 'success' | 'failed' | 'unsupported' | 'delegated'; // ✅ Task 95: Add status field
  timestamp: number;
}

export type TerminalEventTypes = {
  'agent-output': TerminalAgentOutput;
  'system-message': TerminalSystemMessage;
  'clear-terminal': {};
  // ✅ Task 94: New events for manual command support
  'terminal:manual-command': TerminalManualCommand;
  'terminal:agent-output': TerminalAgentResponse;
}

type EventListener<T> = (data: T) => void;

export class TerminalEventBus {
  private static instance: TerminalEventBus;
  private listeners: Map<keyof TerminalEventTypes, EventListener<any>[]> = new Map();

  private constructor() {
    // Initialize listener maps
    this.listeners.set('agent-output', []);
    this.listeners.set('system-message', []);
    this.listeners.set('clear-terminal', []);
  }

  public static getInstance(): TerminalEventBus {
    if (!TerminalEventBus.instance) {
      TerminalEventBus.instance = new TerminalEventBus();
    }
    return TerminalEventBus.instance;
  }

  /**
   * Emit an event to all listeners
   */
  public emit<K extends keyof TerminalEventTypes>(
    event: K,
    data: TerminalEventTypes[K]
  ): void {
    const eventListeners = this.listeners.get(event) || [];
    
    console.log(`TerminalEventBus: Emitting ${event} to ${eventListeners.length} listeners`, data);
    
    eventListeners.forEach(listener => {
      try {
        listener(data);
      } catch (error) {
        console.error(`TerminalEventBus: Error in listener for ${event}:`, error);
      }
    });
  }

  /**
   * Subscribe to an event
   */
  public on<K extends keyof TerminalEventTypes>(
    event: K,
    listener: EventListener<TerminalEventTypes[K]>
  ): () => void {
    const eventListeners = this.listeners.get(event) || [];
    eventListeners.push(listener);
    this.listeners.set(event, eventListeners);

    console.log(`TerminalEventBus: Added listener for ${event}. Total: ${eventListeners.length}`);

    // Return unsubscribe function
    return () => {
      const currentListeners = this.listeners.get(event) || [];
      const index = currentListeners.indexOf(listener);
      if (index > -1) {
        currentListeners.splice(index, 1);
        this.listeners.set(event, currentListeners);
        console.log(`TerminalEventBus: Removed listener for ${event}. Remaining: ${currentListeners.length}`);
      }
    };
  }

  /**
   * Remove all listeners for an event
   */
  public removeAllListeners(event?: keyof TerminalEventTypes): void {
    if (event) {
      this.listeners.set(event, []);
      console.log(`TerminalEventBus: Removed all listeners for ${event}`);
    } else {
      this.listeners.clear();
      this.listeners.set('agent-output', []);
      this.listeners.set('system-message', []);
      this.listeners.set('clear-terminal', []);
      console.log('TerminalEventBus: Removed all listeners');
    }
  }

  /**
   * Get listener count for an event
   */
  public getListenerCount(event: keyof TerminalEventTypes): number {
    return (this.listeners.get(event) || []).length;
  }

  /**
   * Convenience methods for common events
   */
  public emitAgentOutput(agentId: string, output: string, options?: {
    agentName?: string;
    command?: string;
    success?: boolean;
  }): void {
    this.emit('agent-output', {
      output,
      agentId,
      agentName: options?.agentName,
      command: options?.command,
      success: options?.success,
      timestamp: Date.now()
    });
  }

  public emitSystemMessage(message: string, type: 'info' | 'warning' | 'error' | 'success' = 'info'): void {
    this.emit('system-message', {
      message,
      type,
      timestamp: Date.now()
    });
  }

  public emitClearTerminal(): void {
    this.emit('clear-terminal', {});
  }

  // ✅ Task 94: Convenience methods for manual command support
  public emitManualCommand(input: string, agentId: string): void {
    this.emit('terminal:manual-command', {
      input,
      agentId,
      timestamp: Date.now()
    });
  }

  public emitAgentResponse(agentId: string, output: string, success?: boolean, status?: 'success' | 'failed' | 'unsupported' | 'delegated'): void {
    this.emit('terminal:agent-output', {
      output,
      agentId,
      success,
      status, // ✅ Task 95: Include status in agent response
      timestamp: Date.now()
    });
  }
}

// Export singleton instance
export const terminalEventBus = TerminalEventBus.getInstance();

// Agent name mapping for display purposes
export const AGENT_DISPLAY_NAMES: Record<string, string> = {
  'intern': 'InternAgent',
  'junior': 'JuniorAgent', 
  'midlevel': 'MidlevelAgent',
  'senior': 'SeniorAgent',
  'architect': 'ArchitectAgent',
  'designer': 'DesignerAgent',
  'tester': 'TesterAgent',
  'researcher': 'ResearcherAgent',
  'micromanager': 'Micromanager'
};

/**
 * Get display name for agent ID
 */
export function getAgentDisplayName(agentId: string): string {
  // Extract agent type from ID (e.g., 'intern-123' -> 'intern')
  const agentType = agentId.split('-')[0].toLowerCase();
  return AGENT_DISPLAY_NAMES[agentType] || agentId;
}
