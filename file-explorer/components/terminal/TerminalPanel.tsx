"use client"

import React, { useState, useRef, useEffect } from 'react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { User, Bo<PERSON>, Wrench, TestTube, Terminal as TerminalIcon, Settings } from 'lucide-react';
import TerminalBootstrap from './TerminalBootstrap';
import { terminalEventBus } from './terminal-event-bus';

// ✅ Task 94: Agent configuration for terminal routing
interface AgentConfig {
  id: string;
  name: string;
  description: string;
  icon: React.ReactNode;
  color: string;
  capabilities: string[];
}

const AVAILABLE_AGENTS: AgentConfig[] = [
  {
    id: 'micromanager',
    name: 'Micromanager',
    description: 'Task orchestration and decomposition',
    icon: <Settings className="w-4 h-4" />,
    color: 'bg-purple-500',
    capabilities: ['Task Planning', 'Coordination', 'Resource Management']
  },
  {
    id: 'intern',
    name: 'Intern Agent',
    description: 'Basic tasks and learning',
    icon: <User className="w-4 h-4" />,
    color: 'bg-green-500',
    capabilities: ['Simple Tasks', 'Documentation', 'Basic Operations']
  },
  {
    id: 'senior-agent',
    name: 'Senior Agent',
    description: 'Complex development tasks',
    icon: <Bot className="w-4 h-4" />,
    color: 'bg-blue-500',
    capabilities: ['Code Review', 'Architecture', 'Complex Problem Solving']
  },
  {
    id: 'designer',
    name: 'Designer Agent',
    description: 'UI/UX design and prototyping',
    icon: <Wrench className="w-4 h-4" />,
    color: 'bg-pink-500',
    capabilities: ['UI Design', 'Prototyping', 'User Experience']
  },
  {
    id: 'tester',
    name: 'Tester Agent',
    description: 'Testing and quality assurance',
    icon: <TestTube className="w-4 h-4" />,
    color: 'bg-orange-500',
    capabilities: ['Test Automation', 'Quality Assurance', 'Bug Detection']
  }
];

interface TerminalPanelProps {
  className?: string;
  onReady?: (terminal: any) => void;
  defaultAgentId?: string;
}

export default function TerminalPanel({
  className = '',
  onReady,
  defaultAgentId = 'senior-agent'
}: TerminalPanelProps) {
  const [activeAgentId, setActiveAgentId] = useState<string>(defaultAgentId);
  const [isAgentMode, setIsAgentMode] = useState<boolean>(false);
  const [terminalInstance, setTerminalInstance] = useState<any>(null);
  const inputBufferRef = useRef<string>('');

  // ✅ Task 96: Command history and autocomplete state
  const [commandHistory, setCommandHistory] = useState<string[]>([]);
  const [historyIndex, setHistoryIndex] = useState<number>(-1);
  const [currentInput, setCurrentInput] = useState<string>('');

  // ✅ Task 96: Known commands for autocomplete
  const KNOWN_COMMANDS = [
    // General commands
    'help', 'clear', 'exit', 'pwd', 'ls', 'cd', 'cat', 'echo', 'whoami', 'date',
    // Agent-specific commands
    'analyze', 'build', 'test', 'run', 'deploy', 'explain', 'summarize',
    'implement', 'create', 'develop', 'design', 'optimize', 'refactor',
    'debug', 'fix', 'integrate', 'validate', 'verify', 'check',
    // File operations
    'file', 'write', 'read', 'modify', 'delete', 'generate',
    // UI/Design commands
    'ui', 'interface', 'component', 'style', 'layout', 'theme',
    // Testing commands
    'spec', 'qa', 'quality', 'coverage', 'automation'
  ];

  const activeAgent = AVAILABLE_AGENTS.find(agent => agent.id === activeAgentId);

  // ✅ Task 96: Helper functions for input management
  const updateTerminalInput = (newInput: string) => {
    if (!terminalInstance) return;

    // Clear current line and redraw with new input
    const promptLength = 2; // "$ " length
    const currentLineLength = promptLength + currentInput.length;

    // Move cursor to beginning of input and clear line
    terminalInstance.write('\r' + ' '.repeat(currentLineLength) + '\r$ ' + newInput);

    // Update state
    setCurrentInput(newInput);
    inputBufferRef.current = newInput;
  };

  const addToHistory = (command: string) => {
    if (command.trim() && !commandHistory.includes(command.trim())) {
      setCommandHistory(prev => [...prev, command.trim()]);
    }
    setHistoryIndex(-1);
  };

  const navigateHistory = (direction: 'up' | 'down') => {
    if (direction === 'up') {
      if (commandHistory.length > 0 && historyIndex < commandHistory.length - 1) {
        const newIndex = historyIndex + 1;
        setHistoryIndex(newIndex);
        const historyCommand = commandHistory[commandHistory.length - 1 - newIndex];
        updateTerminalInput(historyCommand);
      }
    } else {
      if (historyIndex > 0) {
        const newIndex = historyIndex - 1;
        setHistoryIndex(newIndex);
        const historyCommand = commandHistory[commandHistory.length - 1 - newIndex];
        updateTerminalInput(historyCommand);
      } else if (historyIndex === 0) {
        setHistoryIndex(-1);
        updateTerminalInput('');
      }
    }
  };

  const autocompleteCommand = () => {
    const input = currentInput.trim();
    if (!input) return;

    // Find matching commands
    const matches = KNOWN_COMMANDS.filter(cmd => cmd.startsWith(input.toLowerCase()));

    if (matches.length === 1) {
      // Single match - complete it
      updateTerminalInput(matches[0]);
    } else if (matches.length > 1) {
      // Multiple matches - show them
      terminalInstance?.writeln(`\r\n💡 Available completions: ${matches.join(', ')}`);
      terminalInstance?.write('$ ' + currentInput);
    }
  };

  // ✅ Task 94 Step 1: Handle terminal ready event
  const handleTerminalReady = (terminal: any) => {
    setTerminalInstance(terminal);
    onReady?.(terminal);

    // ✅ Task 94 Step 2: Set up input interception for agent routing
    terminal.onData((input: string) => {
      if (isAgentMode) {
        handleAgentInput(input);
      }
      // Note: Normal PTY input is handled by TerminalBootstrap
    });

    // Display initial agent mode status
    if (isAgentMode) {
      displayAgentModeStatus(terminal);
    }
  };

  // ✅ Task 94 Step 2: Handle input routing to agents (Enhanced with Task 96 features)
  const handleAgentInput = (input: string) => {
    // ✅ Task 96: Handle special keys for history and autocomplete
    if (input === '\r') {
      // Enter key - process command
      if (currentInput.trim()) {
        addToHistory(currentInput.trim());
        processAgentCommand(currentInput.trim());
        setCurrentInput('');
        inputBufferRef.current = '';
      }
      terminalInstance?.write('\r\n$ ');
    } else if (input === '\u007F') {
      // Backspace
      if (currentInput.length > 0) {
        const newInput = currentInput.slice(0, -1);
        updateTerminalInput(newInput);
      }
    } else if (input === '\u0003') {
      // Ctrl+C - cancel current input
      setCurrentInput('');
      inputBufferRef.current = '';
      setHistoryIndex(-1);
      terminalInstance?.write('^C\r\n$ ');
    } else if (input === '\t') {
      // ✅ Task 96: Tab key - autocomplete
      autocompleteCommand();
    } else if (input === '\u001b[A') {
      // ✅ Task 96: Arrow Up - navigate history up
      navigateHistory('up');
    } else if (input === '\u001b[B') {
      // ✅ Task 96: Arrow Down - navigate history down
      navigateHistory('down');
    } else if (input === '\u001b[C' || input === '\u001b[D') {
      // Arrow Left/Right - ignore for now (could implement cursor movement later)
      return;
    } else if (input.charCodeAt(0) >= 32 && input.charCodeAt(0) <= 126) {
      // Regular printable character
      const newInput = currentInput + input;
      updateTerminalInput(newInput);
    }
  };

  // ✅ Task 94 Step 3: Process agent commands
  const processAgentCommand = async (command: string) => {
    if (!activeAgent || !terminalInstance) return;

    try {
      // Display command being processed
      terminalInstance.writeln(`\r\n🤖 [${activeAgent.name}] Processing: ${command}`);

      // ✅ Task 96: Create enhanced context with command history
      const context = {
        input: command,
        agentId: activeAgentId,
        timestamp: Date.now(),
        // ✅ Task 96: Include command history for agent awareness
        previousCommands: commandHistory.slice(-5), // Last 5 commands
        historyContext: {
          totalCommands: commandHistory.length,
          recentCommands: commandHistory.slice(-3),
          isRepeatedCommand: commandHistory.includes(command)
        }
      };

      // ✅ Task 94 Step 3: Route to agent via message bus or agent manager
      terminalEventBus.emit('terminal:manual-command', context);

      // Show processing indicator with history context
      if (commandHistory.includes(command)) {
        terminalInstance.writeln(`🔄 Repeated command detected - routing to ${activeAgent.name}...`);
      } else {
        terminalInstance.writeln(`⏳ Routing command to ${activeAgent.name}...`);
      }

    } catch (error) {
      terminalInstance.writeln(`❌ Error processing command: ${error instanceof Error ? error.message : String(error)}`);
      terminalInstance.write('$ ');
    }
  };

  // ✅ Display agent mode status
  const displayAgentModeStatus = (terminal: any) => {
    if (!activeAgent) return;

    terminal.writeln('\r\n🤖 Agent Terminal Mode Activated');
    terminal.writeln(`📋 Active Agent: ${activeAgent.name}`);
    terminal.writeln(`📝 Description: ${activeAgent.description}`);
    terminal.writeln(`🔧 Capabilities: ${activeAgent.capabilities.join(', ')}`);
    terminal.writeln('💡 Type commands to route them to the selected agent');
    terminal.writeln('🔄 Use the dropdown above to switch agents');
    terminal.writeln('⚡ Toggle "Agent Mode" to switch between direct terminal and agent routing');
    terminal.write('\r\n$ ');
  };

  // ✅ Handle agent mode toggle
  const handleAgentModeToggle = () => {
    const newMode = !isAgentMode;
    setIsAgentMode(newMode);

    if (terminalInstance) {
      if (newMode) {
        terminalInstance.writeln('\r\n🤖 Switched to Agent Mode');
        displayAgentModeStatus(terminalInstance);
      } else {
        terminalInstance.writeln('\r\n💻 Switched to Direct Terminal Mode');
        terminalInstance.writeln('Commands will now be executed directly in the shell');
      }
    }
  };

  // ✅ Handle agent selection change
  const handleAgentChange = (newAgentId: string) => {
    setActiveAgentId(newAgentId);
    const newAgent = AVAILABLE_AGENTS.find(agent => agent.id === newAgentId);
    
    if (terminalInstance && isAgentMode && newAgent) {
      terminalInstance.writeln(`\r\n🔄 Switched to ${newAgent.name}`);
      terminalInstance.writeln(`📝 ${newAgent.description}`);
      terminalInstance.write('$ ');
    }
  };

  // ✅ Task 94 Step 4: Listen for agent responses
  useEffect(() => {
    const unsubscribe = terminalEventBus.on('terminal:agent-output', ({ output, agentId, success, status }) => {
      if (terminalInstance && agentId === activeAgentId) {
        // ✅ Task 95: Handle different response statuses with appropriate colors
        if (status === 'unsupported') {
          // Yellow color for unsupported commands
          terminalInstance.write(`\r\n\x1b[33m🧠 ${activeAgent?.name} does not support this command\x1b[0m\r\n`);
          terminalInstance.writeln(`\x1b[33m${output}\x1b[0m`);
        } else if (status === 'delegated') {
          // Blue color for delegated commands
          terminalInstance.write(`\r\n\x1b[34m🔄 ${activeAgent?.name} delegated command\x1b[0m\r\n`);
          terminalInstance.writeln(`\x1b[34m${output}\x1b[0m`);
        } else if (success === false) {
          // Red color for failed commands
          terminalInstance.write(`\r\n\x1b[31m❌ Error from ${activeAgent?.name}:\x1b[0m\r\n`);
          terminalInstance.writeln(`\x1b[31m${output}\x1b[0m`);
        } else {
          // Green color for successful commands
          terminalInstance.writeln(`\r\n\x1b[32m✅ Response from ${activeAgent?.name}:\x1b[0m`);
          terminalInstance.writeln(output);
        }
        terminalInstance.write('\r\n$ ');
      }
    });

    return unsubscribe;
  }, [terminalInstance, activeAgentId, activeAgent]);

  return (
    <div className={`terminal-panel-container ${className}`}>
      {/* ✅ Task 94 Step 1: Agent Context Selector */}
      <div className="terminal-header bg-gray-900 border-b border-gray-700 p-3 flex items-center gap-3">
        <div className="flex items-center gap-2">
          <TerminalIcon className="w-5 h-5 text-gray-400" />
          <span className="text-sm font-medium text-gray-300">Terminal</span>
        </div>

        <div className="flex items-center gap-3 ml-auto">
          {/* Agent Mode Toggle */}
          <Button
            variant={isAgentMode ? "default" : "outline"}
            size="sm"
            onClick={handleAgentModeToggle}
            className="text-xs"
          >
            {isAgentMode ? '🤖 Agent Mode' : '💻 Direct Mode'}
          </Button>

          {/* Agent Selector */}
          <div className="flex items-center gap-2">
            <span className="text-xs text-gray-400">Agent:</span>
            <Select value={activeAgentId} onValueChange={handleAgentChange}>
              <SelectTrigger className="w-48 h-8 text-xs">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {AVAILABLE_AGENTS.map((agent) => (
                  <SelectItem key={agent.id} value={agent.id}>
                    <div className="flex items-center gap-2">
                      <div className={`w-2 h-2 rounded-full ${agent.color}`} />
                      {agent.icon}
                      <span>{agent.name}</span>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Active Agent Badge */}
          {activeAgent && (
            <Badge variant="secondary" className="text-xs">
              <div className="flex items-center gap-1">
                <div className={`w-2 h-2 rounded-full ${activeAgent.color}`} />
                {activeAgent.name}
              </div>
            </Badge>
          )}
        </div>
      </div>

      {/* Terminal Content */}
      <div className="terminal-content flex-1">
        <TerminalBootstrap
          className="h-full"
          onReady={handleTerminalReady}
        />
      </div>
    </div>
  );
}
