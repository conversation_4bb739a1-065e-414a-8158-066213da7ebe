// components/background/file-operations.ts

export interface FileOperation {
  id: string;
  type: 'read' | 'write' | 'create' | 'delete' | 'move' | 'copy' | 'mkdir' | 'rmdir';
  path: string;
  targetPath?: string; // for move/copy operations
  content?: string; // for write/create operations
  options?: FileOperationOptions;
  timestamp: number;
  agentId?: string;
  transactionId?: string;
  status: 'pending' | 'executing' | 'completed' | 'failed' | 'rolled_back';
  result?: any;
  error?: string;
  metadata?: Record<string, any>;
}

export interface FileOperationOptions {
  encoding?: 'utf8' | 'binary' | 'base64';
  createDirectories?: boolean;
  overwrite?: boolean;
  backup?: boolean;
  permissions?: string; // e.g., '755', '644'
  recursive?: boolean; // for directory operations
  preserveTimestamps?: boolean;
  validatePath?: boolean;
  maxFileSize?: number; // in bytes
  allowedExtensions?: string[];
  blockedPaths?: string[];
}

export interface FileOperationResult {
  success: boolean;
  operation: FileOperation;
  data?: any;
  error?: string;
  warnings?: string[];
  metadata?: Record<string, any>;
}

export interface FilePermissions {
  read: boolean;
  write: boolean;
  execute: boolean;
  delete: boolean;
  create: boolean;
}

export interface SecurityPolicy {
  allowedPaths: string[];
  blockedPaths: string[];
  allowedExtensions: string[];
  blockedExtensions: string[];
  maxFileSize: number;
  maxOperationsPerSecond: number;
  requireAgentAuth: boolean;
  sandboxMode: boolean;
}

export interface FileOperationStats {
  totalOperations: number;
  successfulOperations: number;
  failedOperations: number;
  operationsByType: Record<string, number>;
  averageExecutionTime: number;
  totalBytesRead: number;
  totalBytesWritten: number;
  activeOperations: number;
  lastOperationTime?: number;
}

class FileOperationsManagerCore {
  private operations: Map<string, FileOperation> = new Map();
  private operationHistory: FileOperation[] = [];
  private maxHistorySize = 1000;
  private securityPolicy: SecurityPolicy;
  private stats: FileOperationStats = {
    totalOperations: 0,
    successfulOperations: 0,
    failedOperations: 0,
    operationsByType: {},
    averageExecutionTime: 0,
    totalBytesRead: 0,
    totalBytesWritten: 0,
    activeOperations: 0
  };
  private rateLimitMap: Map<string, number[]> = new Map(); // agentId -> timestamps

  constructor(securityPolicy?: Partial<SecurityPolicy>) {
    this.securityPolicy = {
      allowedPaths: this.getDynamicAllowedPaths(), // Use dynamic paths based on active project
      blockedPaths: ['/etc', '/usr', '/bin', '/sbin', '/var', '/tmp'],
      allowedExtensions: ['.js', '.ts', '.jsx', '.tsx', '.json', '.md', '.txt', '.css', '.html', '.xml', '.yaml', '.yml'],
      blockedExtensions: ['.exe', '.bat', '.sh', '.cmd', '.com', '.scr', '.pif'],
      maxFileSize: 10 * 1024 * 1024, // 10MB
      maxOperationsPerSecond: 100,
      requireAgentAuth: true,
      sandboxMode: true,
      ...securityPolicy
    };
  }

  /**
   * Get dynamic allowed paths based on active project
   */
  private getDynamicAllowedPaths(): string[] {
    try {
      // Try to get active project path
      const { activeProjectService } = require('../services/active-project-service');
      const activeProjectPath = activeProjectService.getActiveProjectPath();

      if (activeProjectPath) {
        console.log(`FileOperations: Using active project path: ${activeProjectPath}`);
        return [activeProjectPath];
      }
    } catch (error) {
      console.warn('FileOperations: Failed to get active project path, falling back to process.cwd():', error);
    }

    // Fallback to current working directory
    return [process.cwd()];
  }

  /**
   * Execute a file operation with security validation
   */
  async executeOperation(operation: Omit<FileOperation, 'id' | 'timestamp' | 'status'>): Promise<FileOperationResult> {
    const operationId = this.generateOperationId();
    const fullOperation: FileOperation = {
      ...operation,
      id: operationId,
      timestamp: Date.now(),
      status: 'pending'
    };

    // Store operation
    this.operations.set(operationId, fullOperation);

    try {
      // Security validation
      const securityCheck = await this.validateSecurity(fullOperation);
      if (!securityCheck.valid) {
        throw new Error(`Security validation failed: ${securityCheck.reason}`);
      }

      // Rate limiting
      if (fullOperation.agentId) {
        const rateLimitCheck = this.checkRateLimit(fullOperation.agentId);
        if (!rateLimitCheck) {
          throw new Error('Rate limit exceeded');
        }
      }

      // Update status
      fullOperation.status = 'executing';

      // Execute the operation
      const startTime = Date.now();
      const result = await this.performOperation(fullOperation);
      const executionTime = Date.now() - startTime;

      // Update operation status
      fullOperation.status = 'completed';
      fullOperation.result = result;

      // Update statistics
      this.updateStats(fullOperation, executionTime, true);

      // Add to history
      this.addToHistory(fullOperation);

      return {
        success: true,
        operation: fullOperation,
        data: result,
        metadata: {
          executionTime,
          operationId
        }
      };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);

      // Update operation status
      fullOperation.status = 'failed';
      fullOperation.error = errorMessage;

      // Update statistics
      this.updateStats(fullOperation, 0, false);

      // Add to history
      this.addToHistory(fullOperation);

      return {
        success: false,
        operation: fullOperation,
        error: errorMessage
      };
    } finally {
      // Clean up active operation
      this.operations.delete(operationId);
    }
  }

  /**
   * Read file content
   */
  async readFile(path: string, options?: FileOperationOptions, agentId?: string): Promise<FileOperationResult> {
    return this.executeOperation({
      type: 'read',
      path,
      options,
      agentId
    });
  }

  /**
   * Write file content
   */
  async writeFile(path: string, content: string, options?: FileOperationOptions, agentId?: string): Promise<FileOperationResult> {
    return this.executeOperation({
      type: 'write',
      path,
      content,
      options,
      agentId
    });
  }

  /**
   * Create new file
   */
  async createFile(path: string, content: string = '', options?: FileOperationOptions, agentId?: string): Promise<FileOperationResult> {
    return this.executeOperation({
      type: 'create',
      path,
      content,
      options,
      agentId
    });
  }

  /**
   * Delete file
   */
  async deleteFile(path: string, options?: FileOperationOptions, agentId?: string): Promise<FileOperationResult> {
    return this.executeOperation({
      type: 'delete',
      path,
      options,
      agentId
    });
  }

  /**
   * Move file
   */
  async moveFile(sourcePath: string, targetPath: string, options?: FileOperationOptions, agentId?: string): Promise<FileOperationResult> {
    return this.executeOperation({
      type: 'move',
      path: sourcePath,
      targetPath,
      options,
      agentId
    });
  }

  /**
   * Copy file
   */
  async copyFile(sourcePath: string, targetPath: string, options?: FileOperationOptions, agentId?: string): Promise<FileOperationResult> {
    return this.executeOperation({
      type: 'copy',
      path: sourcePath,
      targetPath,
      options,
      agentId
    });
  }

  /**
   * Create directory
   */
  async createDirectory(path: string, options?: FileOperationOptions, agentId?: string): Promise<FileOperationResult> {
    return this.executeOperation({
      type: 'mkdir',
      path,
      options,
      agentId
    });
  }

  /**
   * Remove directory
   */
  async removeDirectory(path: string, options?: FileOperationOptions, agentId?: string): Promise<FileOperationResult> {
    return this.executeOperation({
      type: 'rmdir',
      path,
      options,
      agentId
    });
  }

  /**
   * Get operation statistics
   */
  getStats(): FileOperationStats {
    return { ...this.stats };
  }

  /**
   * Get operation history
   */
  getOperationHistory(limit?: number): FileOperation[] {
    const history = [...this.operationHistory];
    return limit ? history.slice(-limit) : history;
  }

  /**
   * Get active operations
   */
  getActiveOperations(): FileOperation[] {
    return Array.from(this.operations.values());
  }

  /**
   * Update security policy
   */
  updateSecurityPolicy(policy: Partial<SecurityPolicy>): void {
    this.securityPolicy = { ...this.securityPolicy, ...policy };
  }

  /**
   * Get current security policy
   */
  getSecurityPolicy(): SecurityPolicy {
    return { ...this.securityPolicy };
  }

  /**
   * Clear operation history
   */
  clearHistory(): void {
    this.operationHistory = [];
  }

  /**
   * Shutdown file operations manager
   */
  shutdown(): void {
    // Cancel all active operations
    for (const operation of this.operations.values()) {
      operation.status = 'failed';
      operation.error = 'System shutdown';
    }

    this.operations.clear();
    this.rateLimitMap.clear();
    console.log('File operations manager shutdown');
  }

  // Private implementation methods
  private generateOperationId(): string {
    return `op-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  private async validateSecurity(operation: FileOperation): Promise<{ valid: boolean; reason?: string }> {
    const path = require('path');

    // Refresh allowed paths to get current active project
    this.securityPolicy.allowedPaths = this.getDynamicAllowedPaths();

    // Normalize paths
    const normalizedPath = path.resolve(operation.path);
    const normalizedTargetPath = operation.targetPath ? path.resolve(operation.targetPath) : undefined;

    // Check if path is allowed
    const isPathAllowed = this.securityPolicy.allowedPaths.some(allowedPath =>
      normalizedPath.startsWith(path.resolve(allowedPath))
    );

    if (!isPathAllowed) {
      return { valid: false, reason: `Path not in allowed directories: ${normalizedPath}. Allowed: ${this.securityPolicy.allowedPaths.join(', ')}` };
    }

    // Check if path is blocked
    const isPathBlocked = this.securityPolicy.blockedPaths.some(blockedPath =>
      normalizedPath.startsWith(path.resolve(blockedPath))
    );

    if (isPathBlocked) {
      return { valid: false, reason: `Path is blocked: ${normalizedPath}` };
    }

    // Check file extension
    const ext = path.extname(normalizedPath).toLowerCase();

    if (this.securityPolicy.allowedExtensions.length > 0 &&
        !this.securityPolicy.allowedExtensions.includes(ext)) {
      return { valid: false, reason: `File extension not allowed: ${ext}` };
    }

    if (this.securityPolicy.blockedExtensions.includes(ext)) {
      return { valid: false, reason: `File extension is blocked: ${ext}` };
    }

    // Check target path for move/copy operations
    if (normalizedTargetPath) {
      const targetIsAllowed = this.securityPolicy.allowedPaths.some(allowedPath =>
        normalizedTargetPath.startsWith(path.resolve(allowedPath))
      );

      if (!targetIsAllowed) {
        return { valid: false, reason: `Target path not in allowed directories: ${normalizedTargetPath}` };
      }

      const targetIsBlocked = this.securityPolicy.blockedPaths.some(blockedPath =>
        normalizedTargetPath.startsWith(path.resolve(blockedPath))
      );

      if (targetIsBlocked) {
        return { valid: false, reason: `Target path is blocked: ${normalizedTargetPath}` };
      }
    }

    // Check file size for write operations
    if ((operation.type === 'write' || operation.type === 'create') && operation.content) {
      const contentSize = Buffer.byteLength(operation.content, 'utf8');
      if (contentSize > this.securityPolicy.maxFileSize) {
        return { valid: false, reason: `File size exceeds limit: ${contentSize} > ${this.securityPolicy.maxFileSize}` };
      }
    }

    // Check agent authentication if required
    if (this.securityPolicy.requireAgentAuth && !operation.agentId) {
      return { valid: false, reason: 'Agent authentication required' };
    }

    return { valid: true };
  }

  private checkRateLimit(agentId: string): boolean {
    const now = Date.now();
    const windowMs = 1000; // 1 second window

    // Get or create rate limit tracking for this agent
    if (!this.rateLimitMap.has(agentId)) {
      this.rateLimitMap.set(agentId, []);
    }

    const timestamps = this.rateLimitMap.get(agentId)!;

    // Remove old timestamps outside the window
    const validTimestamps = timestamps.filter(timestamp => now - timestamp < windowMs);

    // Check if we're under the limit
    if (validTimestamps.length >= this.securityPolicy.maxOperationsPerSecond) {
      return false;
    }

    // Add current timestamp
    validTimestamps.push(now);
    this.rateLimitMap.set(agentId, validTimestamps);

    return true;
  }

  private async performOperation(operation: FileOperation): Promise<any> {
    // This would integrate with the actual file system
    // For browser environment, we'll use the existing Electron API
    if (typeof window !== 'undefined' && window.electronAPI) {
      return this.performElectronOperation(operation);
    } else {
      // For Node.js environment, use direct file system operations
      return this.performNodeOperation(operation);
    }
  }

  private async performElectronOperation(operation: FileOperation): Promise<any> {
    const { electronAPI } = window;

    switch (operation.type) {
      case 'read':
        const readResult = await electronAPI.readFile(operation.path);
        if (!readResult.success) {
          throw new Error(readResult.error);
        }
        this.stats.totalBytesRead += Buffer.byteLength(readResult.content, 'utf8');
        return readResult.content;

      case 'write':
      case 'create':
        const writeResult = await electronAPI.saveFile(operation.path, operation.content || '');
        if (!writeResult.success) {
          throw new Error(writeResult.error);
        }
        this.stats.totalBytesWritten += Buffer.byteLength(operation.content || '', 'utf8');
        return { success: true };

      case 'delete':
        const deleteResult = await electronAPI.deleteFile(operation.path);
        if (!deleteResult.success) {
          throw new Error(deleteResult.error);
        }
        return { success: true };

      case 'move':
        // Implement move as copy + delete
        const readForMove = await electronAPI.readFile(operation.path);
        if (!readForMove.success) {
          throw new Error(readForMove.error);
        }

        const writeForMove = await electronAPI.saveFile(operation.targetPath!, readForMove.content);
        if (!writeForMove.success) {
          throw new Error(writeForMove.error);
        }

        const deleteForMove = await electronAPI.deleteFile(operation.path);
        if (!deleteForMove.success) {
          // Try to clean up the copied file
          await electronAPI.deleteFile(operation.targetPath!);
          throw new Error(deleteForMove.error);
        }

        return { success: true };

      case 'copy':
        const readForCopy = await electronAPI.readFile(operation.path);
        if (!readForCopy.success) {
          throw new Error(readForCopy.error);
        }

        const writeForCopy = await electronAPI.saveFile(operation.targetPath!, readForCopy.content);
        if (!writeForCopy.success) {
          throw new Error(writeForCopy.error);
        }

        return { success: true };

      default:
        throw new Error(`Unsupported operation type: ${operation.type}`);
    }
  }

  private async performNodeOperation(operation: FileOperation): Promise<any> {
    // This would be implemented for Node.js environment
    // For now, throw an error as we're primarily targeting Electron
    throw new Error('Node.js file operations not implemented in browser environment');
  }

  private updateStats(operation: FileOperation, executionTime: number, success: boolean): void {
    this.stats.totalOperations++;

    if (success) {
      this.stats.successfulOperations++;
    } else {
      this.stats.failedOperations++;
    }

    // Update operations by type
    this.stats.operationsByType[operation.type] = (this.stats.operationsByType[operation.type] || 0) + 1;

    // Update average execution time
    if (executionTime > 0) {
      const totalTime = this.stats.averageExecutionTime * (this.stats.totalOperations - 1) + executionTime;
      this.stats.averageExecutionTime = totalTime / this.stats.totalOperations;
    }

    // Update active operations count
    this.stats.activeOperations = this.operations.size;
    this.stats.lastOperationTime = Date.now();
  }

  private addToHistory(operation: FileOperation): void {
    this.operationHistory.push({ ...operation });

    // Maintain history size limit
    if (this.operationHistory.length > this.maxHistorySize) {
      this.operationHistory = this.operationHistory.slice(-this.maxHistorySize);
    }
  }
}

// Global file operations manager instance
export const globalFileOperations = new FileOperationsManagerCore();

// ✅ Export singleton manager class for compatibility with agent execution service
export class FileOperationsManager {
  private static instance: FileOperationsManager;
  private fileOperations: FileOperationsManagerCore;

  private constructor() {
    this.fileOperations = globalFileOperations;
  }

  public static getInstance(): FileOperationsManager {
    if (!FileOperationsManager.instance) {
      FileOperationsManager.instance = new FileOperationsManager();
    }
    return FileOperationsManager.instance;
  }

  // Delegate all methods to the underlying FileOperationsManagerCore instance
  async createFile(filePath: string, content: string, options?: FileOperationOptions, agentId?: string): Promise<FileOperationResult> {
    return this.fileOperations.createFile(filePath, content, options, agentId);
  }

  async readFile(filePath: string, options?: FileOperationOptions, agentId?: string): Promise<FileOperationResult> {
    return this.fileOperations.readFile(filePath, options, agentId);
  }

  async updateFile(filePath: string, content: string, options?: FileOperationOptions, agentId?: string): Promise<FileOperationResult> {
    return this.fileOperations.updateFile(filePath, content, options, agentId);
  }

  async deleteFile(filePath: string, options?: FileOperationOptions, agentId?: string): Promise<FileOperationResult> {
    return this.fileOperations.deleteFile(filePath, options, agentId);
  }

  async copyFile(sourcePath: string, destinationPath: string, options?: FileOperationOptions, agentId?: string): Promise<FileOperationResult> {
    return this.fileOperations.copyFile(sourcePath, destinationPath, options, agentId);
  }

  async moveFile(sourcePath: string, destinationPath: string, options?: FileOperationOptions, agentId?: string): Promise<FileOperationResult> {
    return this.fileOperations.moveFile(sourcePath, destinationPath, options, agentId);
  }

  async createDirectory(dirPath: string, options?: FileOperationOptions, agentId?: string): Promise<FileOperationResult> {
    return this.fileOperations.createDirectory(dirPath, options, agentId);
  }

  async listDirectory(dirPath: string, options?: FileOperationOptions, agentId?: string): Promise<FileOperationResult> {
    return this.fileOperations.listDirectory(dirPath, options, agentId);
  }

  async deleteDirectory(dirPath: string, options?: FileOperationOptions, agentId?: string): Promise<FileOperationResult> {
    return this.fileOperations.deleteDirectory(dirPath, options, agentId);
  }

  getOperationHistory(limit?: number): FileOperation[] {
    return this.fileOperations.getOperationHistory(limit);
  }

  getStats(): FileOperationStats {
    return this.fileOperations.getStats();
  }

  clearHistory(): void {
    return this.fileOperations.clearHistory();
  }
}
