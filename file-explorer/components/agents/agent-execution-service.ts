// components/agents/agent-execution-service.ts
import { MonacoIntegrationManager } from '../background/monaco-integration';
import { FileOperationsManager } from '../background/file-operations';

import { boardIPCBridge } from '../kanban/lib/board-ipc-bridge';
import { AgentContext } from './agent-base';
import { withTimeout, TimeoutError, isTimeoutError } from '../../lib/utils/timeout';
import { executionLogger } from './agent-execution-trace';
import { taskOutputLoggingService } from '../services/task-output-logging-service';
import { terminalEventBus, getAgentDisplayName } from '../terminal/terminal-event-bus';

export interface ExecutionResult {
  success: boolean;
  output: string;
  files?: Array<{ path: string; content: string; action: 'created' | 'modified' | 'deleted' }>;

  kanbanUpdates?: Array<{ cardId: string; action: string; result: any }>;
  error?: string;
  metadata?: Record<string, any>;
}

export interface FileCreationRequest {
  path: string;
  content: string;
  language?: string;
  openInEditor?: boolean;
}



export interface KanbanUpdateRequest {
  cardId?: string;
  action: 'create' | 'update' | 'move' | 'complete';
  data: any;
}

export class AgentExecutionService {
  private static instance: AgentExecutionService;
  private monacoManager: MonacoIntegrationManager;
  private fileManager: FileOperationsManager;


  constructor() {
    this.monacoManager = MonacoIntegrationManager.getInstance();
    this.fileManager = FileOperationsManager.getInstance();

  }

  public static getInstance(): AgentExecutionService {
    if (!AgentExecutionService.instance) {
      AgentExecutionService.instance = new AgentExecutionService();
    }
    return AgentExecutionService.instance;
  }

  /**
   * Create files and optionally open them in Monaco editor
   */
  async createFiles(files: FileCreationRequest[], agentId: string, taskId?: string): Promise<ExecutionResult> {
    const results: Array<{ path: string; content: string; action: 'created' | 'modified' | 'deleted' }> = [];
    const errors: string[] = [];

    console.log(`AgentExecutionService: Creating ${files.length} files for agent ${agentId}`);

    // ✅ Task 67: Log file operation start
    executionLogger.logEvent({
      agentId,
      action: 'file_operation',
      details: `Starting file creation: ${files.length} files`,
      status: 'running',
      metadata: { fileCount: files.length, filePaths: files.map(f => f.path) }
    });

    // ✅ Task 84: Write file operation start to terminal and log
    if (taskId) {
      const operationMsg = `Starting file creation: ${files.length} files`;
      taskOutputLoggingService.addLogEntry(taskId, agentId, operationMsg, 'system', {
        fileCount: files.length,
        filePaths: files.map(f => f.path)
      });


    }

    for (const file of files) {
      const startTime = Date.now();
      try {
        // Create the file
        const result = await this.fileManager.createFile(file.path, file.content, {
          encoding: 'utf8',
          createDirectories: true
        }, agentId);

        if (result.success) {
          results.push({
            path: file.path,
            content: file.content,
            action: 'created'
          });

          // ✅ Task 84: Write file creation success to terminal and log
          if (taskId) {
            const successMsg = `Created file: ${file.path} (${file.content.length} chars)`;
            taskOutputLoggingService.addLogEntry(taskId, agentId, successMsg, 'output', {
              operation: 'create',
              fileSize: file.content.length,
              filePath: file.path
            });


          }

          // Open in Monaco editor if requested
          if (file.openInEditor) {
            try {
              // Note: In a real implementation, we'd need to trigger the editor to open this file
              // For now, we'll log the intent
              console.log(`AgentExecutionService: File ${file.path} marked for editor opening`);
            } catch (editorError) {
              console.warn(`Failed to open ${file.path} in editor:`, editorError);
            }
          }

          console.log(`AgentExecutionService: Created file ${file.path} (${file.content.length} chars)`);

          // ✅ Task 67: Log successful file creation
          executionLogger.logEvent({
            agentId,
            action: 'file_operation',
            targetFile: file.path,
            details: `Successfully created file: ${file.path}`,
            status: 'completed',
            duration: Date.now() - startTime,
            metadata: { operation: 'create', fileSize: file.content.length }
          });
        } else {
          const errorMsg = `Failed to create ${file.path}: ${result.error}`;
          errors.push(errorMsg);

          // ✅ Task 84: Write file creation error to terminal and log
          if (taskId) {
            taskOutputLoggingService.writeAgentErrorToTerminal(taskId, agentId, errorMsg);
          }

          // ✅ Task 67: Log file creation failure
          executionLogger.logEvent({
            agentId,
            action: 'error',
            targetFile: file.path,
            details: `Failed to create file: ${file.path} - ${result.error}`,
            status: 'failed',
            duration: Date.now() - startTime,
            metadata: { operation: 'create', error: result.error }
          });
        }
      } catch (error) {
        const errorMsg = `Error creating ${file.path}: ${error instanceof Error ? error.message : String(error)}`;
        errors.push(errorMsg);
        console.error(`AgentExecutionService: ${errorMsg}`);

        // ✅ Task 67: Log file creation error
        executionLogger.logEvent({
          agentId,
          action: 'error',
          targetFile: file.path,
          details: errorMsg,
          status: 'failed',
          duration: Date.now() - startTime,
          metadata: { operation: 'create', error: error instanceof Error ? error.message : String(error) }
        });
      }
    }

    return {
      success: errors.length === 0,
      output: errors.length === 0
        ? `Successfully created ${results.length} files`
        : `Created ${results.length} files with ${errors.length} errors`,
      files: results,
      error: errors.length > 0 ? errors.join('; ') : undefined,
      metadata: {
        filesCreated: results.length,
        errors: errors.length,
        agentId
      }
    };
  }



  /**
   * Update Kanban board
   */
  async updateKanban(updates: KanbanUpdateRequest[], agentId: string): Promise<ExecutionResult> {
    const results: Array<{ cardId: string; action: string; result: any }> = [];
    const errors: string[] = [];

    console.log(`AgentExecutionService: Processing ${updates.length} Kanban updates for agent ${agentId}`);

    for (const update of updates) {
      try {
        let result: any;

        switch (update.action) {
          case 'create':
            result = await boardIPCBridge.createCard('main', 'column-1', {
              title: update.data.title || 'Agent Task',
              description: update.data.description || '',
              priority: update.data.priority || 'medium',
              ...update.data
            });
            break;

          case 'update':
            if (update.cardId) {
              result = await boardIPCBridge.updateCard('main', update.cardId, update.data);
            }
            break;

          case 'move':
            if (update.cardId && update.data.columnId) {
              result = await boardIPCBridge.moveCard('main', update.cardId, 'current', update.data.columnId, 'swimlane-1');
            }
            break;

          case 'complete':
            if (update.cardId) {
              result = await boardIPCBridge.updateCardProgress('main', update.cardId, 100, agentId);
            }
            break;

          default:
            throw new Error(`Unknown Kanban action: ${update.action}`);
        }

        if (result) {
          results.push({
            cardId: update.cardId || result.id || 'unknown',
            action: update.action,
            result
          });
          console.log(`AgentExecutionService: Kanban ${update.action} successful`);
        }
      } catch (error) {
        const errorMsg = `Kanban ${update.action} failed: ${error instanceof Error ? error.message : String(error)}`;
        errors.push(errorMsg);
        console.error(`AgentExecutionService: ${errorMsg}`);
      }
    }

    return {
      success: errors.length === 0,
      output: `Processed ${results.length} Kanban updates`,
      kanbanUpdates: results,
      error: errors.length > 0 ? errors.join('; ') : undefined,
      metadata: {
        updatesProcessed: results.length,
        errors: errors.length,
        agentId
      }
    };
  }

  /**
   * Comprehensive execution method that handles multiple types of work
   */
  async executeWork(
    context: AgentContext,
    agentId: string,
    work: {
      files?: FileCreationRequest[];
      kanban?: KanbanUpdateRequest[];
    },
    timeoutMs?: number,
    taskId?: string // ✅ Task 84: Add taskId parameter for logging
  ): Promise<ExecutionResult> {
    // ✅ Task 84: Initialize task log if taskId provided
    if (taskId) {
      taskOutputLoggingService.initializeTaskLog(taskId, agentId);
    }

    // ✅ Apply timeout if specified
    if (timeoutMs) {
      return withTimeout(
        this.executeWorkInternal(context, agentId, work, taskId),
        timeoutMs,
        `Agent ${agentId} work execution`
      );
    }

    return this.executeWorkInternal(context, agentId, work, taskId);
  }

  /**
   * Internal work execution method (without timeout wrapper)
   */
  private async executeWorkInternal(
    context: AgentContext,
    agentId: string,
    work: {
      files?: FileCreationRequest[];
      kanban?: KanbanUpdateRequest[];
    },
    taskId?: string // ✅ Task 84: Add taskId parameter
  ): Promise<ExecutionResult> {
    const allResults: ExecutionResult[] = [];
    const allFiles: Array<{ path: string; content: string; action: 'created' | 'modified' | 'deleted' }> = [];
    const allOutputs: string[] = [];
    const allErrors: string[] = [];

    console.log(`AgentExecutionService: Executing comprehensive work for agent ${agentId}`);

    // Execute file operations
    if (work.files && work.files.length > 0) {
      const fileResult = await this.createFiles(work.files, agentId, taskId); // ✅ Task 84: Pass taskId
      allResults.push(fileResult);
      if (fileResult.files) allFiles.push(...fileResult.files);
      allOutputs.push(fileResult.output);
      if (fileResult.error) allErrors.push(fileResult.error);
    }



    // Execute Kanban updates
    if (work.kanban && work.kanban.length > 0) {
      const kanbanResult = await this.updateKanban(work.kanban, agentId);
      allResults.push(kanbanResult);
      allOutputs.push(kanbanResult.output);
      if (kanbanResult.error) allErrors.push(kanbanResult.error);
    }

    const overallSuccess = allErrors.length === 0;
    const combinedOutput = allOutputs.join('\n\n');

    console.log(`AgentExecutionService: Work execution complete. Success: ${overallSuccess}, Files: ${allFiles.length}, Errors: ${allErrors.length}`);

    // ✅ Task 84: Complete task log if taskId provided
    if (taskId) {
      taskOutputLoggingService.completeTaskLog(taskId, overallSuccess ? 'completed' : 'failed');
    }

    return {
      success: overallSuccess,
      output: combinedOutput,
      files: allFiles.length > 0 ? allFiles : undefined,

      kanbanUpdates: allResults.find(r => r.kanbanUpdates)?.kanbanUpdates,
      error: allErrors.length > 0 ? allErrors.join('; ') : undefined,
      metadata: {
        totalOperations: allResults.length,
        filesCreated: allFiles.length,
        errors: allErrors.length,
        agentId,
        context: {
          task: context.task,
          timestamp: Date.now()
        }
      }
    };
  }

  /**
   * ✅ Task 91: Execute shell command through secure interface
   */
  async executeShellCommand(agentId: string, command: string, taskId?: string): Promise<ExecutionResult> {
    if (!command || typeof command !== 'string') {
      const errorMsg = 'Invalid command: command must be a non-empty string';
      console.error(`AgentExecutionService: ${errorMsg}`);

      if (taskId) {
        taskOutputLoggingService.addLogEntry(taskId, agentId, errorMsg, 'error');
      }

      return {
        success: false,
        output: '',
        error: errorMsg,
        metadata: { agentId, command: command || 'undefined' }
      };
    }

    const startTime = Date.now();
    console.log(`AgentExecutionService: Agent ${agentId} executing shell command: ${command}`);

    // ✅ Task 91: Log shell command start
    executionLogger.logEvent({
      agentId,
      action: 'shell_command',
      details: `Executing shell command: ${command}`,
      status: 'running',
      metadata: { command }
    });

    // ✅ Task 91: Write shell command start to terminal and log
    if (taskId) {
      const commandMsg = `Executing shell command: ${command}`;
      taskOutputLoggingService.addLogEntry(taskId, agentId, commandMsg, 'system', {
        command,
        operation: 'shell_command'
      });
    }

    // ✅ Task 92: Emit command start to terminal UI
    const agentDisplayName = getAgentDisplayName(agentId);
    terminalEventBus.emitSystemMessage(
      `[${agentDisplayName}] Executing: ${command}`,
      'info'
    );

    try {
      // Check if we have access to the terminal API
      if (typeof window !== 'undefined' && window.electronAPI?.terminal) {
        const result = await window.electronAPI.terminal.agentCommand({ command, agentId });

        if (result.success) {
          const successMsg = `Shell command completed successfully: ${command}`;
          console.log(`AgentExecutionService: ${successMsg}`);

          // ✅ Task 91: Log successful shell command execution
          executionLogger.logEvent({
            agentId,
            action: 'shell_command',
            details: successMsg,
            status: 'completed',
            duration: Date.now() - startTime,
            metadata: { command, output: result.output }
          });

          // ✅ Task 91: Write shell command success to terminal and log
          if (taskId) {
            taskOutputLoggingService.addLogEntry(taskId, agentId, successMsg, 'output', {
              command,
              output: result.output,
              operation: 'shell_command'
            });
          }

          // ✅ Task 92: Emit successful output to terminal UI
          if (result.output) {
            terminalEventBus.emitAgentOutput(agentId, result.output, {
              agentName: agentDisplayName,
              command,
              success: true
            });
          }

          return {
            success: true,
            output: result.output || '',
            metadata: {
              agentId,
              command,
              executionTime: Date.now() - startTime
            }
          };
        } else {
          const errorMsg = `Shell command failed: ${result.error}`;
          console.error(`AgentExecutionService: ${errorMsg}`);

          // ✅ Task 91: Log shell command failure
          executionLogger.logEvent({
            agentId,
            action: 'error',
            details: errorMsg,
            status: 'failed',
            duration: Date.now() - startTime,
            metadata: { command, error: result.error }
          });

          // ✅ Task 91: Write shell command error to terminal and log
          if (taskId) {
            taskOutputLoggingService.writeAgentErrorToTerminal(taskId, agentId, errorMsg);
          }

          // ✅ Task 92: Emit error to terminal UI
          terminalEventBus.emitSystemMessage(
            `[${agentDisplayName}] Error: ${result.error}`,
            'error'
          );

          return {
            success: false,
            output: '',
            error: result.error,
            metadata: { agentId, command }
          };
        }
      } else {
        const errorMsg = 'Terminal API not available - cannot execute shell commands';
        console.warn(`AgentExecutionService: ${errorMsg}`);

        // ✅ Task 91: Log API unavailable
        executionLogger.logEvent({
          agentId,
          action: 'error',
          details: errorMsg,
          status: 'failed',
          duration: Date.now() - startTime,
          metadata: { command, error: 'api_unavailable' }
        });

        if (taskId) {
          taskOutputLoggingService.writeAgentErrorToTerminal(taskId, agentId, errorMsg);
        }

        // ✅ Task 92: Emit API unavailable error to terminal UI
        terminalEventBus.emitSystemMessage(
          `[${agentDisplayName}] ${errorMsg}`,
          'error'
        );

        return {
          success: false,
          output: '',
          error: errorMsg,
          metadata: { agentId, command }
        };
      }
    } catch (error) {
      const errorMsg = `Shell command execution error: ${error instanceof Error ? error.message : String(error)}`;
      console.error(`AgentExecutionService: ${errorMsg}`);

      // ✅ Task 91: Log shell command error
      executionLogger.logEvent({
        agentId,
        action: 'error',
        details: errorMsg,
        status: 'failed',
        duration: Date.now() - startTime,
        metadata: { command, error: error instanceof Error ? error.message : String(error) }
      });

      if (taskId) {
        taskOutputLoggingService.writeAgentErrorToTerminal(taskId, agentId, errorMsg);
      }

      // ✅ Task 92: Emit execution error to terminal UI
      terminalEventBus.emitSystemMessage(
        `[${agentDisplayName}] ${errorMsg}`,
        'error'
      );

      return {
        success: false,
        output: '',
        error: errorMsg,
        metadata: { agentId, command }
      };
    }
  }

  /**
   * Get execution statistics
   */
  getExecutionStats(): {
    files: any;
    monaco: any;
  } {
    return {
      files: this.fileManager.getStats(),

      monaco: this.monacoManager.getStats()
    };
  }
}
