// components/agents/agent-manager-complete.ts
import { AgentBase, AgentConfig, AgentContext, AgentResponse, AgentMessage, AgentStatus, TaskAssignment } from './agent-base'; // Import AgentStatus and TaskAssignment from agent-base
import { withTimeout, TimeoutError, isTimeoutError } from '../../lib/utils/timeout';
import { getGlobalConcurrencyManager, updateGlobalConcurrencyLimit } from '../../lib/utils/concurrency-manager';
import { debugAgent, debugTiming, debugError, debugState } from '../../lib/utils/debug';
import { trackAgentEvent, trackPerformance, trackError } from '../../lib/utils/telemetry';
import { budgetEnforcer } from '../../lib/budget-enforcer';
import { BudgetExceededError, isBudgetExceededError } from '../../lib/budget-error';

// Import all agent types
import { MicromanagerAgent, TaskDecomposition, SubTask, TaskDependency } from './micromanager-agent'; // Import decomposition types
import { InternAgent } from './implementation/intern-agent';
import { JuniorAgent } from './implementation/junior-agent';
import { MidLevelAgent } from './implementation/midlevel-agent';
import { SeniorAgent } from './implementation/senior-agent';
import { ResearcherAgent } from './specialized/researcher-agent';
import { ArchitectAgent } from './specialized/architect-agent';
import { DesignerAgent } from './specialized/designer-agent';
import { TesterAgent } from './specialized/tester-agent'; // Ensure TesterAgent is imported if used in getRecommendedAgentForCard

// Import middleware components
import { AgentStateMonitorAgent } from '../middleware/agent-state-monitor';
import { ErrorResolutionCoordinatorAgent } from '../middleware/error-resolution-coordinator';
import { ContinuousLearningAgent } from '../middleware/continuous-learning-agent';
import { TaskClassifierAgent } from '../middleware/task-classifier';
import { ResourceOptimizerAgent } from '../middleware/resource-optimizer';
import { ContextProvider } from '../middleware/context-provider';
import { ResultValidator } from '../middleware/result-validator';
import { ExecutionManager } from '../middleware/execution-manager';

// Import IPC bridges for cross-window sync
import { boardIPCBridge } from '../kanban/lib/board-ipc-bridge';
import { agentIPCBridge } from '@/lib/agent-ipc-bridge';

// Import file operations for agent file system access
import { FileOperationsManager } from '../background/file-operations';

// Import model optimization for cost-aware model selection
import { modelOptimizer, ModelSelectionCriteria } from '../../lib/model-optimizer';
import { getProviderConfig } from './llm-provider-registry';

// Import agent events service for live presence tracking
import { getAgentEventsService } from '../../services/agent-events';

// ✅ Task 81: Import agent work tracker for load monitoring and balancing
import { agentWorkTracker, AgentWorkTracker } from './agent-work-tracker';

// ✅ Task 94: Import terminal event bus for manual command support
import { terminalEventBus } from '../terminal/terminal-event-bus';

// ✅ MCP Protocol Support
import { mcpInitializationService } from '../../lib/mcp-initialization-service';

// ✅ Task 93: Import agent shell service for Kanban shell command execution
import { agentShellService } from '../../services/agent-shell-service';

// ✅ Task 72: Enhanced IBoardAgentService with full Kanban API access for Micromanager
interface IBoardAgentService {
  // Card operations
  createTaskCard(task: any, agentId: string): Promise<any>;
  moveCardToColumn(cardId: string, columnId: string, agentId: string): Promise<any>;
  addCardDependency(cardId: string, dependencyCardId: string, agentId: string): Promise<any>;
  updateCardProgress(cardId: string, progress: number, agentId: string): Promise<any>;

  // ✅ Full CRUD operations for Micromanager
  createBoard?(boardName: string, agentId: string): Promise<any>;
  addColumn?(boardId: string, columnName: string, agentId: string): Promise<any>;
  removeColumn?(boardId: string, columnId: string, agentId: string): Promise<any>;
  addSwimlane?(boardId: string, swimlaneName: string, agentId: string): Promise<any>;
  removeSwimlane?(boardId: string, swimlaneId: string, agentId: string): Promise<any>;
  assignAgent?(cardId: string, agentId: string, assignerAgentId: string): Promise<any>;
  linkCardToOrchestration?(cardId: string, orchestrationId: string, agentId: string): Promise<any>;
  deleteCard?(cardId: string, agentId: string): Promise<any>;
}

// Implementation of IBoardAgentService using IPC bridge
class BoardAgentService implements IBoardAgentService {
  async createTaskCard(task: any, agentId: string): Promise<any> {
    try {
      // Use board IPC bridge to create card and sync across windows
      const result = await boardIPCBridge.createCard('default-board', 'column-1', {
        title: task.title || task.description || 'Agent Task',
        description: task.description || task.title || '',
        priority: task.priority || 'medium',
        projectId: task.projectId || '',
        tags: task.tags || [],
        progress: 0,
        labels: [],
        agentAssignments: [{ agentId, assignedAt: new Date().toISOString() }],
        dependencies: [],
        resourceMetrics: { tokenUsage: 0, cpuTime: 0, memoryUsage: 0 },
        taskHistory: [{
          timestamp: new Date().toISOString(),
          action: 'created',
          agentId,
          details: `Task created by agent ${agentId}`
        }]
      });

      // Broadcast agent action via IPC
      await agentIPCBridge.addMessage({
        agentId,
        message: `Created Kanban card: ${task.title || task.description}`,
        timestamp: Date.now(),
        type: 'info'
      });

      return result;
    } catch (error) {
      console.error('Failed to create task card via IPC:', error);
      return null;
    }
  }

  async moveCardToColumn(cardId: string, columnId: string, agentId: string): Promise<any> {
    try {
      // ✅ Use the enhanced moveCardToColumn method that handles source column detection
      const result = await boardIPCBridge.moveCardToColumn('main', cardId, columnId, agentId);

      if (result) {
        // Broadcast agent action via IPC
        await agentIPCBridge.addMessage({
          agentId,
          message: `Moved card ${cardId} to column ${columnId}`,
          timestamp: Date.now(),
          type: 'info'
        });
        console.log(`BoardAgentService: Successfully moved card ${cardId} to column ${columnId}`);
      } else {
        console.warn(`BoardAgentService: Failed to move card ${cardId} to column ${columnId} - no result returned`);
      }

      return result;
    } catch (error) {
      console.error('BoardAgentService: Failed to move card via IPC:', error);
      return null;
    }
  }

  async addCardDependency(cardId: string, dependencyCardId: string, agentId: string): Promise<any> {
    try {
      // Note: This would need to be implemented in board IPC bridge
      console.log(`Agent ${agentId} adding dependency ${dependencyCardId} to card ${cardId}`);

      // Broadcast agent action via IPC
      await agentIPCBridge.addMessage({
        agentId,
        message: `Added dependency ${dependencyCardId} to card ${cardId}`,
        timestamp: Date.now(),
        type: 'info'
      });

      return true;
    } catch (error) {
      console.error('Failed to add card dependency:', error);
      return null;
    }
  }

  async updateCardProgress(cardId: string, progress: number, agentId: string): Promise<any> {
    try {
      // ✅ Use the new updateCardProgress method from board IPC bridge
      const result = await boardIPCBridge.updateCardProgress('main', cardId, progress, agentId);

      if (result) {
        // Broadcast agent action via IPC
        await agentIPCBridge.addMessage({
          agentId,
          message: `Updated card ${cardId} progress to ${progress}%`,
          timestamp: Date.now(),
          type: 'info'
        });
        console.log(`BoardAgentService: Successfully updated card ${cardId} progress to ${progress}%`);
      } else {
        console.warn(`BoardAgentService: Failed to update card ${cardId} progress to ${progress}% - no result returned`);
      }

      return result;
    } catch (error) {
      console.error('BoardAgentService: Failed to update card progress via IPC:', error);
      return null;
    }
  }

  // ✅ Task 79: Real CRUD operations for Micromanager using IPC bridge
  async createBoard?(boardName: string, agentId: string): Promise<any> {
    try {
      console.log(`BoardAgentService: Creating board "${boardName}" for agent ${agentId}`);

      // ✅ Use real boardIPCBridge to create board
      const newBoard = await boardIPCBridge.createBoard(boardName, `Board created by agent ${agentId}`);

      if (newBoard) {
        // Log success message
        await agentIPCBridge.addMessage({
          agentId,
          message: `Successfully created board: ${boardName} (ID: ${newBoard.id})`,
          timestamp: Date.now(),
          type: 'info'
        });

        console.log(`BoardAgentService: Successfully created board "${boardName}" with ID: ${newBoard.id}`);
        return { success: true, board: newBoard, agentId };
      } else {
        throw new Error('Board creation returned null - IPC bridge may not be available');
      }
    } catch (error) {
      console.error('BoardAgentService: Failed to create board:', error);

      // Log error message
      await agentIPCBridge.addMessage({
        agentId,
        message: `Failed to create board: ${boardName} - ${error instanceof Error ? error.message : 'Unknown error'}`,
        timestamp: Date.now(),
        type: 'error'
      });

      throw new Error(`Micromanager failed to create Kanban board: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  async addColumn?(boardId: string, columnName: string, agentId: string): Promise<any> {
    try {
      console.log(`BoardAgentService: Adding column "${columnName}" to board ${boardId} for agent ${agentId}`);

      // ✅ Use real boardIPCBridge to add column
      const newColumn = await boardIPCBridge.addColumn(boardId, columnName);

      if (newColumn) {
        // Log success message
        await agentIPCBridge.addMessage({
          agentId,
          message: `Successfully added column: ${columnName} to board ${boardId} (Column ID: ${newColumn.id})`,
          timestamp: Date.now(),
          type: 'info'
        });

        console.log(`BoardAgentService: Successfully added column "${columnName}" with ID: ${newColumn.id} to board ${boardId}`);
        return { success: true, column: newColumn, boardId, agentId };
      } else {
        throw new Error('Column creation returned null - IPC bridge may not be available or board not found');
      }
    } catch (error) {
      console.error('BoardAgentService: Failed to add column:', error);

      // Log error message
      await agentIPCBridge.addMessage({
        agentId,
        message: `Failed to add column: ${columnName} to board ${boardId} - ${error instanceof Error ? error.message : 'Unknown error'}`,
        timestamp: Date.now(),
        type: 'error'
      });

      throw new Error(`Micromanager failed to manage Kanban columns: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  async removeColumn?(boardId: string, columnId: string, agentId: string): Promise<any> {
    try {
      console.log(`BoardAgentService: Removing column ${columnId} from board ${boardId} for agent ${agentId}`);
      await agentIPCBridge.addMessage({
        agentId,
        message: `Removed column: ${columnId} from board ${boardId}`,
        timestamp: Date.now(),
        type: 'info'
      });
      return { success: true, boardId, columnId, agentId };
    } catch (error) {
      console.error('BoardAgentService: Failed to remove column:', error);
      throw new Error(`Micromanager lacks permission to remove Kanban columns: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  async addSwimlane?(boardId: string, swimlaneName: string, agentId: string): Promise<any> {
    try {
      console.log(`BoardAgentService: Adding swimlane "${swimlaneName}" to board ${boardId} for agent ${agentId}`);
      await agentIPCBridge.addMessage({
        agentId,
        message: `Added swimlane: ${swimlaneName} to board ${boardId}`,
        timestamp: Date.now(),
        type: 'info'
      });
      return { success: true, boardId, swimlaneName, agentId };
    } catch (error) {
      console.error('BoardAgentService: Failed to add swimlane:', error);
      throw new Error(`Micromanager lacks permission to manage Kanban swimlanes: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  async removeSwimlane?(boardId: string, swimlaneId: string, agentId: string): Promise<any> {
    try {
      console.log(`BoardAgentService: Removing swimlane ${swimlaneId} from board ${boardId} for agent ${agentId}`);
      await agentIPCBridge.addMessage({
        agentId,
        message: `Removed swimlane: ${swimlaneId} from board ${boardId}`,
        timestamp: Date.now(),
        type: 'info'
      });
      return { success: true, boardId, swimlaneId, agentId };
    } catch (error) {
      console.error('BoardAgentService: Failed to remove swimlane:', error);
      throw new Error(`Micromanager lacks permission to remove Kanban swimlanes: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  async assignAgent?(cardId: string, agentId: string, assignerAgentId: string): Promise<any> {
    try {
      console.log(`BoardAgentService: Assigning agent ${agentId} to card ${cardId} by ${assignerAgentId}`);
      await agentIPCBridge.addMessage({
        agentId: assignerAgentId,
        message: `Assigned agent ${agentId} to card ${cardId}`,
        timestamp: Date.now(),
        type: 'info'
      });
      return { success: true, cardId, agentId, assignerAgentId };
    } catch (error) {
      console.error('BoardAgentService: Failed to assign agent:', error);
      throw new Error(`Micromanager lacks permission to assign agents to Kanban cards: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  async linkCardToOrchestration?(cardId: string, orchestrationId: string, agentId: string): Promise<any> {
    try {
      console.log(`BoardAgentService: Linking card ${cardId} to orchestration ${orchestrationId} for agent ${agentId}`);
      await agentIPCBridge.addMessage({
        agentId,
        message: `Linked card ${cardId} to orchestration ${orchestrationId}`,
        timestamp: Date.now(),
        type: 'info'
      });
      return { success: true, cardId, orchestrationId, agentId };
    } catch (error) {
      console.error('BoardAgentService: Failed to link card to orchestration:', error);
      throw new Error(`Micromanager lacks permission to link cards to orchestration: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  async deleteCard?(cardId: string, agentId: string): Promise<any> {
    try {
      console.log(`BoardAgentService: Deleting card ${cardId} for agent ${agentId}`);
      await agentIPCBridge.addMessage({
        agentId,
        message: `Deleted card: ${cardId}`,
        timestamp: Date.now(),
        type: 'info'
      });
      return { success: true, cardId, agentId };
    } catch (error) {
      console.error('BoardAgentService: Failed to delete card:', error);
      throw new Error(`Micromanager lacks permission to delete Kanban cards: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }
}

// Removed AgentStatus and TaskAssignment interfaces (now in agent-base.ts)

export interface SystemMetrics {
  totalTasks: number;
  successfulTasks: number;
  failedTasks: number;
  averageResponseTime: number;
  totalTokensUsed: number;
  systemHealthScore: number;
  activeAgents: number;
  queueLength: number;
}

/**
 * ✅ Task Rejection Reasons
 * Enum for categorizing why tasks are rejected before execution
 */
export enum TaskRejectedReason {
  BudgetExceeded = 'BUDGET_EXCEEDED',
  AgentUnavailable = 'AGENT_UNAVAILABLE',
  InvalidContext = 'INVALID_CONTEXT',
  ResourceLimits = 'RESOURCE_LIMITS',
  SystemError = 'SYSTEM_ERROR'
}

/**
 * ✅ Task Rejection Error
 * Custom error for task rejections with detailed information
 */
export class TaskRejectionError extends Error {
  constructor(
    public readonly reason: TaskRejectedReason,
    public readonly taskId: string,
    public readonly agentId: string,
    message: string,
    public readonly details?: any
  ) {
    super(message);
    this.name = 'TaskRejectionError';
  }
}

export class CompleteAgentManager {
  private agents: Map<string, AgentBase> = new Map();
  private agentStatuses: Map<string, AgentStatus> = new Map();
  private taskQueue: TaskAssignment[] = [];
  private activeTasks: Map<string, TaskAssignment> = new Map();
  private taskHistory: TaskAssignment[] = [];
  private messageListeners: ((message: AgentMessage) => void)[] = [];

  // ✅ Concurrency management
  private concurrencyManager = getGlobalConcurrencyManager(3); // Default limit

  // Middleware components
  private stateMonitor: AgentStateMonitorAgent;
  private errorCoordinator: ErrorResolutionCoordinatorAgent;
  private learningAgent: ContinuousLearningAgent;
  private taskClassifier: TaskClassifierAgent;
  private resourceOptimizer: ResourceOptimizerAgent;
  private contextProvider: ContextProvider;
  private resultValidator: ResultValidator;
  private executionManager: ExecutionManager;

  // Board interaction service
  private boardService: IBoardAgentService; // Add board service

  // File operations manager for cross-window file system sync
  private fileOperations: FileOperationsManager;

  // Agent events service for live presence tracking
  private eventsService = getAgentEventsService();

  // ✅ Task 81: Agent work tracker for load monitoring and balancing
  private workTracker = agentWorkTracker;

  // ✅ Settings integration for proper agent configuration
  private settingsManager: any;
  private llmIntegration: any;
  private initialized = false;

  // System metrics
  private systemMetrics: SystemMetrics = {
    totalTasks: 0,
    successfulTasks: 0,
    failedTasks: 0,
    averageResponseTime: 0,
    totalTokensUsed: 0,
    systemHealthScore: 100,
    activeAgents: 0,
    queueLength: 0
  };

  constructor() { // Remove boardService parameter - use IPC bridge instead
    this.boardService = new BoardAgentService(); // Create IPC-enabled board service
    this.fileOperations = new FileOperationsManager(); // Create file operations manager
    this.initializeMiddleware();

    // ✅ Initialize asynchronously to wait for settings
    this.initializeAsync();
  }

  private async initializeAsync(): Promise<void> {
    try {
      // ✅ Initialize LLM integration and wait for settings
      await this.initializeLLMIntegration();

      // ✅ Initialize agents with proper configuration
      await this.initializeAgents();

      // ✅ Start monitoring after everything is ready
      this.startSystemMonitoring();

      // ✅ Task 77: Set up Kanban assignment event listeners and fallback polling
      this.setupKanbanEventListeners();
      this.setupFallbackPolling();

      // ✅ Task 94: Set up terminal command routing
      this.setupTerminalCommandHandling();

      // ✅ MCP Protocol Support: Initialize MCP connections
      await this.initializeMCP();

      this.initialized = true;
      console.log('✅ CompleteAgentManager: Fully initialized with proper configuration');
    } catch (error) {
      console.error('❌ CompleteAgentManager: Failed to initialize:', error);
      throw error;
    }
  }

  private async initializeLLMIntegration(): Promise<void> {
    try {
      // Import and initialize LLM integration service
      const { llmIntegration } = await import('./llm-integration-service');
      this.llmIntegration = llmIntegration;

      await this.llmIntegration.initialize();
      this.settingsManager = this.llmIntegration.getSettingsManager();

      console.log('✅ CompleteAgentManager: LLM integration initialized');
    } catch (error) {
      console.error('❌ CompleteAgentManager: Failed to initialize LLM integration:', error);
      throw error;
    }
  }

  // ✅ MCP Protocol Support: Initialize MCP connections
  private async initializeMCP(): Promise<void> {
    try {
      console.log('🔌 MCP: Initializing MCP connections for agent system...');

      const result = await mcpInitializationService.initialize();

      if (result.success) {
        console.log(`✅ MCP: Initialized successfully. Connected servers: ${result.connectedServers.join(', ')}`);

        if (result.failedServers.length > 0) {
          console.warn(`⚠️ MCP: Some servers failed to connect: ${result.failedServers.join(', ')}`);
        }
      } else {
        console.warn('⚠️ MCP: No MCP servers connected. Agents will use standard LLM execution.');
      }
    } catch (error) {
      console.error('❌ MCP: Failed to initialize MCP connections:', error);
      // Don't throw error - MCP is optional, agents can still work without it
    }
  }

  private initializeMiddleware(): void {
    // Initialize middleware components
    this.stateMonitor = new AgentStateMonitorAgent({
      id: 'state_monitor',
      name: 'State Monitor',
      type: 'middleware'
    });

    this.errorCoordinator = new ErrorResolutionCoordinatorAgent({
      id: 'error_coordinator',
      name: 'Error Coordinator',
      type: 'middleware'
    });

    this.learningAgent = new ContinuousLearningAgent({
      id: 'learning_agent',
      name: 'Learning Agent',
      type: 'middleware'
    });

    this.taskClassifier = new TaskClassifierAgent({
      id: 'task_classifier',
      name: 'Task Classifier',
      type: 'middleware'
    });

    this.resourceOptimizer = new ResourceOptimizerAgent({
      id: 'resource_optimizer',
      name: 'Resource Optimizer',
      type: 'middleware'
    });

    this.contextProvider = new ContextProvider();
    this.resultValidator = new ResultValidator();
    this.executionManager = new ExecutionManager();

    // Set up alert handling
    this.stateMonitor.onAlert((alert) => {
      this.handleHealthAlert(alert);
    });
  }

  private async initializeAgents(): Promise<void> {
    try {
      // ✅ Get agent configurations from settings with full provider/model info
      const settings = this.settingsManager.getSettings();
      console.log('✅ CompleteAgentManager: Loading agent configurations from settings');

      // Convert settings to agent configs with full configuration
      const agentConfigs: AgentConfig[] = settings.agents.map((agentSetting: any) => ({
        id: agentSetting.id,
        name: agentSetting.name,
        type: this.getAgentTypeFromId(agentSetting.id),
        model: agentSetting.model,
        provider: agentSetting.provider,
        maxTokens: agentSetting.maxTokens,
        temperature: agentSetting.temperature
      }));

      console.log('✅ CompleteAgentManager: Agent configurations loaded:', {
        totalAgents: agentConfigs.length,
        agentIds: agentConfigs.map(c => c.id),
        providersUsed: [...new Set(agentConfigs.map(c => c.provider))],
        modelsUsed: [...new Set(agentConfigs.map(c => c.model))]
      });

      agentConfigs.forEach(config => {
        // ✅ Validate configuration before creating agent
        if (!config.provider || !config.model) {
          console.error(`❌ CompleteAgentManager: Invalid configuration for agent ${config.id}:`, {
            provider: config.provider,
            model: config.model
          });
          return;
        }

        const agent = this.createAgent(config);
        if (agent) {
          this.agents.set(config.id, agent);

          // Register with state monitor
          this.stateMonitor.registerAgent(config.id);

          // ✅ Task 81: Register with work tracker for load monitoring
          this.workTracker.registerAgent(config.id, config.type, 3); // Default 3 concurrent tasks

          // Create initial status
          this.agentStatuses.set(config.id, {
            id: config.id,
            name: config.name,
            type: config.type as any,
            status: 'idle',
            lastActivity: Date.now(),
            tokensUsed: 0,
            tasksCompleted: 0,
            errorCount: 0,
            healthScore: 100,
            capabilities: agent.getCapabilities()
          });

          console.log(`✅ CompleteAgentManager: Agent ${config.id} initialized with provider: ${config.provider}, model: ${config.model}`);
        }
      });

      this.updateSystemMetrics();

      // ✅ Reset all agent health scores on initialization to ensure fresh start
      this.workTracker.resetAllAgentHealth();

      console.log(`✅ CompleteAgentManager: ${this.agents.size} agents initialized successfully with reset health scores`);
    } catch (error) {
      console.error('❌ CompleteAgentManager: Failed to initialize agents:', error);
      throw error;
    }
  }

  private getAgentTypeFromId(agentId: string): string {
    if (agentId === 'micromanager') return 'orchestrator';
    if (['intern', 'junior', 'midlevel', 'senior'].includes(agentId)) return 'implementation';
    if (['researcher', 'architect', 'designer', 'tester'].includes(agentId)) return 'specialized';
    return 'unknown';
  }

  // ✅ Task 77: Set up Kanban assignment event listeners for automatic task execution
  private setupKanbanEventListeners(): void {
    try {
      console.log('🎯 Setting up Kanban assignment event listeners for automatic agent execution');

      // Import and use the new Kanban events system
      import('../kanban/lib/kanban-events').then(({ kanbanEvents }) => {
        // Listen for agent assignment events
        kanbanEvents.on('agentAssigned', async (data) => {
          await this.handleAgentAssigned(data);
        });

        // Listen for agent unassignment events
        kanbanEvents.on('agentUnassigned', async (data) => {
          await this.handleAgentUnassigned(data);
        });

        // Listen for card status changes
        kanbanEvents.on('cardStatusChanged', async (data) => {
          await this.handleCardStatusChanged(data);
        });

        console.log('✅ Kanban event listeners registered successfully');
      }).catch(error => {
        console.error('❌ Failed to import Kanban events:', error);
      });

    } catch (error) {
      console.error('❌ Failed to setup Kanban event listeners:', error);
    }
  }

  // ✅ Task 77: Handle agent assignment events from Kanban
  private async handleAgentAssigned(data: any): Promise<void> {
    try {
      console.log('🎯 Agent assignment event received:', data);

      const { cardId, agentId, cardTitle, columnId, boardId } = data;

      // Check if agent exists and is available
      const agentStatus = this.agentStatuses.get(agentId);
      if (!agentStatus) {
        console.warn(`⚠️ Agent ${agentId} not found for card assignment`);
        return;
      }

      // Check agent availability
      if (agentStatus.status !== 'idle') {
        console.log(`⚠️ Agent ${agentId} is not idle (status: ${agentStatus.status}), queuing task`);
      }

      // ✅ Check if this is a Taskmaster-originated assignment
      const isTaskmasterOrigin = await this.isTaskmasterOriginatedCard(cardId, boardId);

      // ✅ Task 93: Get full card data to check for shell command
      const cardData = await this.getCardData(cardId, boardId);

      // Create agent context from event data
      const context: AgentContext = {
        task: `Execute Kanban card: ${cardTitle}`,
        metadata: {
          source: isTaskmasterOrigin ? 'taskmaster_assignment' : 'kanban_assignment',
          cardId: cardId,
          kanbanCardId: cardId,
          assignedAt: Date.now(),
          priority: 'medium',
          boardId: boardId,
          columnId: columnId,
          taskmasterOrigin: isTaskmasterOrigin,
          shellCommand: cardData?.shellCommand  // ✅ Task 93: Include shell command in context
        }
      };

      console.log(`🚀 Auto-executing ${isTaskmasterOrigin ? 'Taskmaster' : 'Kanban'} task for agent ${agentId} from card ${cardId}`);

      // Execute the task automatically
      const taskId = await this.assignTask(agentId, context, 'medium', 3, cardId);

      // ✅ Task 93: Execute shell command if present
      if (cardData?.shellCommand && cardData.shellCommand.trim()) {
        console.log(`🐚 Executing shell command for card ${cardId}: ${cardData.shellCommand}`);

        try {
          const shellResult = await agentShellService.runCommand(cardData.shellCommand, {
            agentId,
            taskId,
            cardId
          });

          if (shellResult.success) {
            console.log(`✅ Shell command completed successfully for card ${cardId}`);

            // Update card status to Done
            await this.boardService.moveCardToColumn(cardId, 'column-4', agentId); // Assuming column-4 is Done

            // Log shell command success
            await agentIPCBridge.addMessage({
              agentId,
              message: `Shell command completed: ${cardData.shellCommand}`,
              timestamp: Date.now(),
              type: 'success',
              metadata: {
                cardId,
                taskId,
                shellCommand: cardData.shellCommand,
                output: shellResult.output.substring(0, 200) // Truncate for logging
              }
            });
          } else {
            console.error(`❌ Shell command failed for card ${cardId}: ${shellResult.error}`);

            // Move card to Error/Failed column if available, otherwise keep in current column
            // await this.boardService.moveCardToColumn(cardId, 'column-error', agentId);

            // Log shell command failure
            await agentIPCBridge.addMessage({
              agentId,
              message: `Shell command failed: ${shellResult.error}`,
              timestamp: Date.now(),
              type: 'error',
              metadata: {
                cardId,
                taskId,
                shellCommand: cardData.shellCommand,
                error: shellResult.error
              }
            });
          }
        } catch (error) {
          console.error(`💥 Shell command execution exception for card ${cardId}:`, error);

          // Log shell command exception
          await agentIPCBridge.addMessage({
            agentId,
            message: `Shell command exception: ${error instanceof Error ? error.message : String(error)}`,
            timestamp: Date.now(),
            type: 'error',
            metadata: {
              cardId,
              taskId,
              shellCommand: cardData.shellCommand,
              exception: error instanceof Error ? error.message : String(error)
            }
          });
        }
      }

      // Log the auto-execution event
      await agentIPCBridge.addMessage({
        agentId,
        message: `Auto-executing ${isTaskmasterOrigin ? 'Taskmaster' : 'Kanban'} task: ${cardTitle}`,
        timestamp: Date.now(),
        type: 'info',
        metadata: { cardId, taskId, autoExecution: true, taskmasterOrigin: isTaskmasterOrigin }
      });

      console.log(`✅ Task ${taskId} auto-assigned to agent ${agentId} for card ${cardId}`);

    } catch (error) {
      console.error('❌ Failed to handle agent assignment:', error);
    }
  }

  // ✅ Task 77: Handle agent unassignment events from Kanban
  private async handleAgentUnassigned(data: any): Promise<void> {
    try {
      console.log('🎯 Agent unassignment event received:', data);

      const { cardId, previousAgentId, cardTitle } = data;

      // Cancel any pending tasks for this card
      const pendingTasks = this.taskQueue.filter(task =>
        task.kanbanCardId === cardId && task.agentId === previousAgentId
      );

      for (const task of pendingTasks) {
        const index = this.taskQueue.indexOf(task);
        if (index > -1) {
          this.taskQueue.splice(index, 1);
          console.log(`🚫 Cancelled pending task ${task.taskId} for unassigned agent ${previousAgentId}`);
        }
      }

      // Log the unassignment event
      await agentIPCBridge.addMessage({
        agentId: previousAgentId,
        message: `Unassigned from Kanban card: ${cardTitle}`,
        timestamp: Date.now(),
        type: 'info',
        metadata: { cardId, unassignment: true }
      });

      console.log(`✅ Agent ${previousAgentId} unassigned from card ${cardId}`);

    } catch (error) {
      console.error('❌ Failed to handle agent unassignment:', error);
    }
  }

  // ✅ Task 77: Handle card status change events from Kanban
  private async handleCardStatusChanged(data: any): Promise<void> {
    try {
      console.log('📊 Card status change event received:', data);

      const { cardId, oldColumnId, newColumnId, cardTitle, assignedAgentId } = data;

      // Track status transitions for agent metrics
      if (assignedAgentId) {
        await agentIPCBridge.addMessage({
          agentId: assignedAgentId,
          message: `Card status updated: ${cardTitle} moved from ${oldColumnId} to ${newColumnId}`,
          timestamp: Date.now(),
          type: 'info',
          metadata: { cardId, statusChange: true, oldColumnId, newColumnId }
        });

        // Emit card status changed event for further processing
        import('../kanban/lib/kanban-events').then(({ emitCardStatusChanged }) => {
          emitCardStatusChanged(cardId, oldColumnId, newColumnId, cardTitle, assignedAgentId);
        });
      }
    } catch (error) {
      console.error('❌ Failed to handle card status change:', error);
    }
  }

  // ✅ Task 77: Fallback polling mechanism to check for assigned cards
  private setupFallbackPolling(): void {
    // Poll every 30 seconds to check for any missed assignments
    setInterval(async () => {
      try {
        await this.checkForMissedAssignments();
      } catch (error) {
        console.error('❌ Error in fallback polling:', error);
      }
    }, 30000); // 30 seconds

    console.log('✅ Fallback polling mechanism started (30s interval)');
  }

  // ✅ Task 94: Set up terminal command routing to agents
  private setupTerminalCommandHandling(): void {
    try {
      console.log('🖥️ Setting up terminal command routing for manual agent commands');

      // Listen for manual commands from terminal
      terminalEventBus.on('terminal:manual-command', async ({ input, agentId, timestamp }) => {
        try {
          console.log(`🤖 Terminal command received: "${input}" for agent: ${agentId}`);

          // Get the agent
          const agent = this.agents.get(agentId);
          if (!agent) {
            console.error(`❌ Agent not found: ${agentId}`);
            terminalEventBus.emitAgentResponse(
              agentId,
              `Error: Agent '${agentId}' not found. Available agents: ${Array.from(this.agents.keys()).join(', ')}`,
              false,
              'failed' // ✅ Task 95: Add status for agent not found
            );
            return;
          }

          // Create context for the command
          const context: AgentContext = {
            task: input,
            source: 'terminal',
            projectPath: await this.getActiveProjectPath(),
            metadata: {
              terminalCommand: true,
              timestamp,
              originalInput: input
            }
          };

          // Execute the command through the agent
          console.log(`⚡ Executing terminal command via ${agentId}:`, input);
          const response = await agent.execute(context);

          // ✅ Task 95: Send response back to terminal with status
          const outputText = response.output || response.content || response.error ||
                           (response.success ? 'Command executed successfully' : 'Command execution failed');

          terminalEventBus.emitAgentResponse(
            agentId,
            outputText,
            response.success,
            response.status || (response.success ? 'success' : 'failed')
          );

          if (response.success) {
            console.log(`✅ Terminal command completed successfully by ${agentId}`);
          } else {
            console.log(`❌ Terminal command failed in ${agentId}:`, response.error);
          }

        } catch (error) {
          console.error('❌ Error processing terminal command:', error);
          terminalEventBus.emitAgentResponse(
            agentId,
            `Error: ${error instanceof Error ? error.message : 'Unknown error occurred'}`,
            false,
            'failed' // ✅ Task 95: Add status for error cases
          );
        }
      });

      console.log('✅ Terminal command routing initialized');
    } catch (error) {
      console.error('❌ Failed to set up terminal command handling:', error);
    }
  }

  // ✅ Helper method to get active project path
  private async getActiveProjectPath(): Promise<string | undefined> {
    try {
      const { activeProjectService } = await import('../services/active-project-service');
      return activeProjectService.getActiveProjectPath();
    } catch (error) {
      console.warn('Failed to get active project path:', error);
      return undefined;
    }
  }

  // ✅ Task 77: Check for cards assigned to agents that might have been missed
  private async checkForMissedAssignments(): Promise<void> {
    try {
      // Get current board state to check for assigned cards
      if (typeof window !== 'undefined' && window.electronAPI) {
        const boardState = await window.electronAPI.ipc?.invoke('board:get-state', 'main');
        if (boardState && boardState.columns) {
          for (const column of boardState.columns) {
            for (const card of column.cards) {
              if (card.assignedAgentId) {
                // Check if this agent has any active tasks for this card
                const hasActiveTask = this.activeTasks.has(card.id) ||
                                    this.taskQueue.some(task => task.kanbanCardId === card.id);

                if (!hasActiveTask) {
                  console.log(`🔄 Found missed assignment: Card ${card.id} assigned to ${card.assignedAgentId}`);

                  // Trigger the assignment handler
                  await this.handleAgentAssigned({
                    cardId: card.id,
                    agentId: card.assignedAgentId,
                    cardTitle: card.title,
                    columnId: card.columnId,
                    boardId: 'main',
                    timestamp: Date.now()
                  });
                }
              }
            }
          }
        }
      }
    } catch (error) {
      console.warn('⚠️ Failed to check for missed assignments:', error);
    }
  }

  // ✅ Check if a card originated from Taskmaster orchestration
  private async isTaskmasterOriginatedCard(cardId: string, boardId: string): Promise<boolean> {
    try {
      // Get card details from board state
      if (typeof window !== 'undefined' && window.electronAPI) {
        const boardState = await window.electronAPI.ipc?.invoke('board:get-state', boardId);
        if (boardState && boardState.columns) {
          for (const column of boardState.columns) {
            const card = column.cards.find((c: any) => c.id === cardId);
            if (card) {
              // Check for Taskmaster indicators in card metadata
              const isTaskmasterCard =
                // Check task history for taskmaster-orchestrator creation
                card.taskHistory?.some((entry: any) =>
                  entry.agentId === 'taskmaster-orchestrator' ||
                  entry.details?.includes('Taskmaster task')
                ) ||
                // Check tags for taskmaster indicators
                card.tags?.some((tag: string) =>
                  tag.includes('taskmaster') ||
                  tag.includes('module:') ||
                  tag.includes('milestone:')
                ) ||
                // Check if projectId matches Taskmaster task ID pattern
                (card.projectId && card.projectId.startsWith('task-')) ||
                // Check if board name indicates Taskmaster origin
                boardState.name?.includes('Taskmaster') ||
                boardState.name?.includes('Module') ||
                boardState.name?.includes('Milestone');

              console.log(`🔍 Card ${cardId} Taskmaster origin check: ${isTaskmasterCard}`);
              return isTaskmasterCard;
            }
          }
        }
      }

      return false;
    } catch (error) {
      console.warn(`⚠️ Failed to check Taskmaster origin for card ${cardId}:`, error);
      return false;
    }
  }

  // ✅ Task 73: Determine task complexity from card data
  private determineTaskComplexity(cardData: any): string {
    const title = (cardData.title || '').toLowerCase();
    const description = (cardData.description || '').toLowerCase();
    const tags = cardData.tags || [];

    // Check for complexity indicators
    const complexityIndicators = {
      simple: ['simple', 'basic', 'trivial', 'quick', 'small'],
      moderate: ['moderate', 'medium', 'standard', 'typical'],
      complex: ['complex', 'advanced', 'difficult', 'large', 'system'],
      advanced: ['architecture', 'design', 'optimization', 'performance', 'security']
    };

    const text = `${title} ${description} ${tags.join(' ')}`;

    for (const [level, indicators] of Object.entries(complexityIndicators)) {
      if (indicators.some(indicator => text.includes(indicator))) {
        return level;
      }
    }

    // Default to moderate if no indicators found
    return 'moderate';
  }

  // ✅ Task 73: Validate agent skills against task requirements
  private validateAgentSkills(taskComplexity: string, agentCapabilities: string[]): boolean {
    const skillRequirements = {
      simple: ['simple_tasks', 'basic_file_operations', 'boilerplate_generation'],
      moderate: ['single_file_implementation', 'moderate_complexity_coding', 'api_integration'],
      complex: ['multi_file_implementation', 'system_integration', 'advanced_algorithms'],
      advanced: ['complex_system_implementation', 'architectural_decisions', 'performance_optimization']
    };

    const requiredSkills = skillRequirements[taskComplexity as keyof typeof skillRequirements] || [];

    // Check if agent has at least one required skill
    return requiredSkills.some(skill => agentCapabilities.includes(skill));
  }

  private createAgent(config: AgentConfig): AgentBase | null {
    switch (config.id) {
      case 'micromanager':
        return new MicromanagerAgent(config);
      case 'intern':
        return new InternAgent(config);
      case 'junior':
        return new JuniorAgent(config);
      case 'midlevel':
        return new MidLevelAgent(config);
      case 'senior':
        return new SeniorAgent(config);
      case 'researcher':
        return new ResearcherAgent(config);
      case 'architect':
        return new ArchitectAgent(config);
      case 'designer':
        return new DesignerAgent(config);
      case 'tester':
        return new TesterAgent(config);
      default:
        console.warn(`Unknown agent type: ${config.id}`);
        return null;
    }
  }

  // ✅ Check if manager is fully initialized
  public isInitialized(): boolean {
    return this.initialized;
  }

  // ✅ Wait for initialization to complete
  public async waitForInitialization(timeoutMs = 30000): Promise<void> {
    const startTime = Date.now();
    while (!this.initialized && (Date.now() - startTime) < timeoutMs) {
      await new Promise(resolve => setTimeout(resolve, 100));
    }

    if (!this.initialized) {
      throw new Error('CompleteAgentManager initialization timeout');
    }
  }

  // Enhanced public API methods
  public async submitTask(
    task: string,
    files?: string[],
    priority: TaskAssignment['priority'] = 'medium',
    metadata?: Record<string, any>
  ): Promise<string> {
    // ✅ Ensure manager is initialized before accepting tasks
    if (!this.initialized) {
      console.log('⏳ CompleteAgentManager: Waiting for initialization before submitting task...');
      await this.waitForInitialization();
    }
    const context: AgentContext = {
      task,
      files,
      metadata: {
        ...metadata,
        submittedAt: Date.now(),
        source: 'user',
        originalTaskId: `user-task-${Date.now()}` // Ensure a unique ID for the initial task
      }
    };

    // Step 1: Classify the task
    // The Micromanager handles initial classification and decomposition, so a high-level user task
    // should generally go to the Micromanager first.
    // The TaskClassifier is more for individual subtasks after decomposition.
    // For a top-level user task, we can bypass TaskClassifier or explicitly route to Micromanager.

    // Force initial user tasks to Micromanager for orchestration
    const initialAgentId = 'micromanager';

    // Step 2: Assign task
    return await this.assignTask(initialAgentId, context, priority);
  }

  public async assignTask(
    agentId: string,
    context: AgentContext,
    priority: TaskAssignment['priority'] = 'medium',
    maxRetries = 3,
    kanbanCardId?: string // Added kanbanCardId to assignment
  ): Promise<string> {
    // ✅ Ensure manager is initialized before assigning tasks
    if (!this.initialized) {
      console.log('⏳ CompleteAgentManager: Waiting for initialization before assigning task...');
      await this.waitForInitialization();
    }

    // ✅ Check for duplicate task assignments for the same Kanban card
    if (kanbanCardId) {
      const existingTask = this.findExistingTaskForCard(kanbanCardId);
      if (existingTask) {
        console.log(`⚠️ Task already exists for Kanban card ${kanbanCardId}: ${existingTask.taskId} (status: ${existingTask.status})`);
        return existingTask.taskId;
      }
    }

    const taskId = `task_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    // Gather enhanced context
    const enhancedContext = await this.enhanceContext(context, agentId);

    const assignment: TaskAssignment = {
      taskId,
      agentId,
      context: { ...enhancedContext, metadata: { ...enhancedContext.metadata, kanbanCardId: kanbanCardId || enhancedContext.metadata?.kanbanCardId } }, // Ensure kanbanCardId is passed
      priority,
      retryCount: 0,
      maxRetries,
      status: 'pending',
      createdAt: Date.now(),
      updatedAt: Date.now(),
      kanbanCardId: kanbanCardId // Stored directly on TaskAssignment for easy access
    };

    this.taskQueue.push(assignment);
    this.systemMetrics.queueLength = this.taskQueue.length;

    // Process queue
    this.processTaskQueue(); // Don't await, let it run in the background

    return taskId;
  }

  private async enhanceContext(context: AgentContext, agentId: string): Promise<AgentContext> {
    try {
      // Inject active project path if not already set
      if (!context.projectPath) {
        try {
          const { activeProjectService } = await import('../services/active-project-service');
          const activeProjectPath = activeProjectService.getActiveProjectPath();
          if (activeProjectPath) {
            context.projectPath = activeProjectPath;
            console.log(`Injected project path for agent ${agentId}: ${activeProjectPath}`);
          } else {
            console.warn(`No active project set for agent ${agentId}`);
          }
        } catch (error) {
          console.warn('Failed to inject project path:', error);
        }
      }

      // Use context provider to gather relevant context
      const contextPackage = await this.contextProvider.gatherContext(
        context.task,
        context.files || []
      );

      // Enhance with project-specific metadata if project path is available
      let projectMetadata = {};
      if (context.projectPath) {
        try {
          projectMetadata = await this.generateProjectMetadata(context.projectPath);
          console.log(`Enhanced context with project metadata for: ${context.projectPath}`);
        } catch (error) {
          console.warn('Failed to generate project metadata:', error);
        }
      }

      return {
        ...context,
        codeContext: contextPackage.relevantCode.join('\n'),
        rules: contextPackage.ruleReferences,
        dependencies: contextPackage.dependencies,
        metadata: {
          ...context.metadata,
          patterns: contextPackage.patterns,
          contextEnhanced: true,
          ...projectMetadata
        }
      };
    } catch (error) {
      console.warn('Context enhancement failed:', error);
      return context;
    }
  }

  /**
   * Generate project-specific metadata for agent context
   */
  private async generateProjectMetadata(projectPath: string): Promise<Record<string, any>> {
    try {
      const projectName = projectPath.split('/').pop() || 'Unknown Project';

      // Generate lightweight project structure
      const projectStructure = await this.generateProjectTree(projectPath);

      // Detect project type and main entry points
      const projectType = await this.detectProjectType(projectPath);
      const mainEntries = await this.findMainEntries(projectPath);

      return {
        projectName,
        projectPath,
        projectType,
        projectStructure,
        mainEntries,
        contextGenerated: Date.now()
      };
    } catch (error) {
      console.warn('Failed to generate project metadata:', error);
      return {};
    }
  }

  /**
   * Generate a lightweight project tree structure
   */
  private async generateProjectTree(projectPath: string, maxDepth: number = 2): Promise<string[]> {
    try {
      if (typeof window !== 'undefined' && window.electronAPI) {
        const result = await window.electronAPI.readDirectory(projectPath);
        if (result.success && result.items) {
          const tree: string[] = [];

          // Add root level items
          for (const item of result.items.slice(0, 20)) { // Limit to first 20 items
            if (item.type === 'folder') {
              tree.push(`📁 ${item.name}/`);

              // Add one level of subdirectories if depth allows
              if (maxDepth > 1) {
                try {
                  const subResult = await window.electronAPI.readDirectory(`${projectPath}/${item.name}`);
                  if (subResult.success && subResult.items) {
                    for (const subItem of subResult.items.slice(0, 5)) { // Limit subdirectory items
                      const icon = subItem.type === 'folder' ? '📁' : '📄';
                      tree.push(`  ${icon} ${subItem.name}${subItem.type === 'folder' ? '/' : ''}`);
                    }
                  }
                } catch (subError) {
                  // Skip subdirectory if it can't be read
                }
              }
            } else {
              tree.push(`📄 ${item.name}`);
            }
          }

          return tree;
        }
      }

      return [`Project: ${projectPath.split('/').pop()}`];
    } catch (error) {
      console.warn('Failed to generate project tree:', error);
      return [];
    }
  }

  /**
   * Detect project type based on files and structure
   */
  private async detectProjectType(projectPath: string): Promise<string> {
    try {
      if (typeof window !== 'undefined' && window.electronAPI) {
        const result = await window.electronAPI.readDirectory(projectPath);
        if (result.success && result.items) {
          const fileNames = result.items.map(item => item.name.toLowerCase());

          // Check for common project indicators
          if (fileNames.includes('package.json')) {
            if (fileNames.includes('next.config.js') || fileNames.includes('next.config.ts')) {
              return 'Next.js';
            }
            if (fileNames.includes('vite.config.js') || fileNames.includes('vite.config.ts')) {
              return 'Vite';
            }
            return 'Node.js';
          }

          if (fileNames.includes('cargo.toml')) return 'Rust';
          if (fileNames.includes('go.mod')) return 'Go';
          if (fileNames.includes('requirements.txt') || fileNames.includes('pyproject.toml')) return 'Python';
          if (fileNames.includes('composer.json')) return 'PHP';
          if (fileNames.includes('pom.xml') || fileNames.includes('build.gradle')) return 'Java';

          // Check for common web files
          if (fileNames.some(name => name.endsWith('.html'))) return 'Web';

          return 'Generic';
        }
      }

      return 'Unknown';
    } catch (error) {
      console.warn('Failed to detect project type:', error);
      return 'Unknown';
    }
  }

  /**
   * Find main entry points in the project
   */
  private async findMainEntries(projectPath: string): Promise<string[]> {
    try {
      if (typeof window !== 'undefined' && window.electronAPI) {
        const result = await window.electronAPI.readDirectory(projectPath);
        if (result.success && result.items) {
          const entries: string[] = [];

          // Common entry point patterns
          const entryPatterns = [
            'index.js', 'index.ts', 'index.tsx', 'index.jsx',
            'main.js', 'main.ts', 'main.tsx', 'main.jsx',
            'app.js', 'app.ts', 'app.tsx', 'app.jsx',
            'server.js', 'server.ts',
            'src/index.js', 'src/index.ts', 'src/index.tsx', 'src/index.jsx',
            'src/main.js', 'src/main.ts', 'src/main.tsx', 'src/main.jsx',
            'src/app.js', 'src/app.ts', 'src/app.tsx', 'src/app.jsx'
          ];

          for (const pattern of entryPatterns) {
            const found = result.items.find(item =>
              item.name.toLowerCase() === pattern.toLowerCase() ||
              item.name.toLowerCase() === pattern.split('/').pop()?.toLowerCase()
            );
            if (found) {
              entries.push(found.name);
            }
          }

          // Check src directory if it exists
          const srcFolder = result.items.find(item =>
            item.type === 'folder' && item.name.toLowerCase() === 'src'
          );
          if (srcFolder && entries.length === 0) {
            try {
              const srcResult = await window.electronAPI.readDirectory(`${projectPath}/src`);
              if (srcResult.success && srcResult.items) {
                const srcEntries = srcResult.items
                  .filter(item => item.type === 'file' &&
                    ['index', 'main', 'app'].some(name =>
                      item.name.toLowerCase().startsWith(name)
                    )
                  )
                  .map(item => `src/${item.name}`);
                entries.push(...srcEntries);
              }
            } catch (srcError) {
              // Skip src directory if it can't be read
            }
          }

          return entries.length > 0 ? entries : ['No main entry detected'];
        }
      }

      return ['Unknown'];
    } catch (error) {
      console.warn('Failed to find main entries:', error);
      return [];
    }
  }

  /**
   * ✅ Update concurrency limit from settings
   */
  public updateConcurrencyLimit(newLimit: number): void {
    this.concurrencyManager.setLimit(newLimit);
    updateGlobalConcurrencyLimit(newLimit);
  }

  /**
   * ✅ Get concurrency statistics
   */
  public getConcurrencyStats() {
    return this.concurrencyManager.getStats();
  }

  /**
   * ✅ Optimize agent model selection based on cost preferences
   */
  private async optimizeAgentModel(
    agentId: string,
    context: AgentContext,
    costSettings: any
  ): Promise<{ provider?: string; model?: string } | null> {
    try {
      if (!costSettings?.preferCheaperModels) {
        // Cost optimization disabled, use default model
        return null;
      }

      // Get agent capabilities to determine model requirements
      const agent = this.agents.get(agentId);
      if (!agent) {
        return null;
      }

      const capabilities = agent.getCapabilities();

      // Estimate token usage based on task complexity
      const taskLength = context.task.length;
      const estimatedInputTokens = Math.max(500, Math.ceil(taskLength / 4)); // ~4 chars per token
      const estimatedOutputTokens = Math.min(4000, Math.max(200, estimatedInputTokens * 0.5));

      // Define selection criteria based on agent type and task
      const criteria: ModelSelectionCriteria = {
        requiredCapabilities: capabilities,
        minContextSize: this.getMinContextSizeForAgent(agentId),
        estimatedInputTokens,
        estimatedOutputTokens,
        // Exclude very expensive providers if budget is tight
        excludedProviders: costSettings.budgetLimit < 50 ? ['openai'] : undefined
      };

      // Get optimal model
      const optimalModel = modelOptimizer.selectOptimalModel(
        criteria,
        costSettings
      );

      if (optimalModel) {
        // ✅ Enhanced user feedback showing optimization details
        const originalProvider = agent.config.provider || 'openai';
        const originalModel = agent.config.model || 'gpt-4';

        // Calculate cost savings if different from original
        if (optimalModel.provider !== originalProvider || optimalModel.modelId !== originalModel) {
          const originalCost = this.calculateOriginalModelCost(originalProvider, originalModel, estimatedInputTokens, estimatedOutputTokens);
          const costSavings = originalCost - optimalModel.totalCostEstimate;
          const savingsPercentage = ((costSavings / originalCost) * 100);

          console.log(`💡 Cost optimization active: Using cheaper model '${optimalModel.provider}/${optimalModel.modelId}' instead of '${originalProvider}/${originalModel}' to reduce cost by $${costSavings.toFixed(4)} (${savingsPercentage.toFixed(1)}% savings)`);
        } else {
          console.log(`🧠 Optimizer: Using optimal model ${optimalModel.provider}/${optimalModel.modelId} (estimated cost: $${optimalModel.totalCostEstimate.toFixed(4)})`);
        }

        return {
          provider: optimalModel.provider,
          model: optimalModel.modelId
        };
      }

      return null;
    } catch (error) {
      console.warn('Model optimization failed, using default model:', error);
      return null;
    }
  }

  /**
   * ✅ Calculate cost for original model configuration
   */
  private calculateOriginalModelCost(provider: string, model: string, inputTokens: number, outputTokens: number): number {
    try {
      const config = getProviderConfig(provider as any);
      const inputCost = (inputTokens / 1000) * config.costPer1kTokens.input;
      const outputCost = (outputTokens / 1000) * config.costPer1kTokens.output;
      return inputCost + outputCost;
    } catch (error) {
      console.warn(`Failed to calculate cost for ${provider}/${model}, using fallback:`, error);
      // Fallback to OpenAI GPT-4 pricing
      return (inputTokens / 1000) * 0.03 + (outputTokens / 1000) * 0.06;
    }
  }

  /**
   * ✅ Get minimum context size requirement for agent
   */
  private getMinContextSizeForAgent(agentId: string): number {
    switch (agentId) {
      case 'micromanager':
        return 8000; // Needs large context for orchestration
      case 'architect':
        return 6000; // Needs context for system design
      case 'senior':
        return 6000; // Complex tasks need more context
      case 'researcher':
        return 4000; // Analysis tasks need decent context
      case 'midlevel':
        return 4000; // Moderate complexity
      case 'designer':
        return 3000; // UI/UX tasks
      case 'junior':
        return 2000; // Simple tasks
      case 'tester':
        return 2000; // Test generation
      case 'intern':
        return 1000; // Basic tasks
      default:
        return 2000; // Conservative default
    }
  }

  public async executeTask(agentId: string, context: AgentContext, timeoutMs?: number): Promise<AgentResponse> {
    const agent = this.agents.get(agentId);
    if (!agent) {
      const error = `Agent with id '${agentId}' not found`;
      debugError(new Error(error), 'executeTask');
      trackError('agent_not_found', agentId);
      throw new Error(error);
    }

    const status = this.agentStatuses.get(agentId);
    if (!status) {
      const error = `Agent status for '${agentId}' not found`;
      debugError(new Error(error), 'executeTask');
      trackError('agent_status_not_found', agentId);
      throw new Error(error);
    }

    debugAgent(`Starting task execution for agent ${agentId}: ${context.task}`);
    trackAgentEvent(agentId, 'task_execution_started', {
      taskLength: context.task.length,
      hasFiles: !!context.files?.length,
      fileCount: context.files?.length || 0
    });

    // Update agent status on start
    const oldStatus = status.status;
    status.status = 'busy';
    status.currentTask = context.task;
    status.lastActivity = Date.now();

    debugState(`Agent ${agentId}`, oldStatus, 'busy');

    // Notify agent events service of task start
    this.eventsService.notifyTaskStarted(
      agentId,
      this.getAgentTypeFromId(agentId),
      `task-${Date.now()}`,
      context.task
    );

    // If this task is linked to a Kanban card, update its status
    const kanbanCardId = context.metadata?.kanbanCardId;
    if (kanbanCardId) {
        // ✅ Check if card is already in progress to prevent duplicate moves
        const currentCardState = await this.getCurrentCardState(kanbanCardId);
        if (currentCardState && currentCardState.columnId !== "column-3") {
            console.log(`🔄 [TASK START] Moving card ${kanbanCardId} (${currentCardState.title}) from ${currentCardState.columnId} to column-3 (In Development) for agent ${agentId}`);
            await this.boardService.moveCardToColumn(kanbanCardId, "column-3", agentId).catch(e => console.error(`Failed to move card ${kanbanCardId}:`, e));
            await this.boardService.updateCardProgress(kanbanCardId, 10, agentId).catch(e => console.error(`Failed to update progress for card ${kanbanCardId}:`, e));
        } else if (currentCardState) {
            console.log(`⚠️ [TASK START] Card ${kanbanCardId} (${currentCardState.title}) is already in development column (${currentCardState.columnId}), skipping move`);
        } else {
            console.warn(`⚠️ [TASK START] Could not get current state for card ${kanbanCardId}, attempting move anyway`);
            await this.boardService.moveCardToColumn(kanbanCardId, "column-3", agentId).catch(e => console.error(`Failed to move card ${kanbanCardId}:`, e));
        }
    }


    const startTime = Date.now();

    try {
      // ✅ Optimize model selection based on cost preferences
      let optimizedConfig = null;
      try {
        // Get cost settings from LLM integration service
        const llmService = require('./llm-integration-service').llmIntegration;
        const settingsManager = llmService.getSettingsManager();
        const settings = settingsManager.getSettings();

        if (settings?.cost?.preferCheaperModels) {
          optimizedConfig = await this.optimizeAgentModel(agentId, context, settings.cost);
        }
      } catch (error) {
        console.warn('Model optimization failed, using default configuration:', error);
      }

      // ✅ Execute task with model optimization if available
      let response;

      if (optimizedConfig && optimizedConfig.provider && optimizedConfig.model) {
        // Temporarily override agent configuration for this execution
        const originalConfig = { ...agent.config };
        agent.config.provider = optimizedConfig.provider as any;
        agent.config.model = optimizedConfig.model;

        console.log(`🧠 Optimizer: Using optimized model ${optimizedConfig.provider}/${optimizedConfig.model} for agent ${agentId}`);

        // Restore original config after execution
        const restoreConfig = () => {
          agent.config.provider = originalConfig.provider;
          agent.config.model = originalConfig.model;
        };

        // ✅ Execute task with optimized model
        response = await this.concurrencyManager.run(
          async () => {
            try {
              if (timeoutMs) {
                return await withTimeout(
                  agent.execute(context),
                  timeoutMs,
                  `Agent ${agentId} task execution`
                );
              } else {
                return await agent.execute(context);
              }
            } finally {
              restoreConfig();
            }
          },
          {
            label: `Agent ${agentId} task execution`,
            priority: context.metadata?.priority === 'high' ? 'high' : 'medium',
            timeout: timeoutMs
          }
        );
      } else {
        // ✅ Execute task with default configuration (no optimization)
        response = await this.concurrencyManager.run(
          async () => {
            if (timeoutMs) {
              return withTimeout(
                agent.execute(context),
                timeoutMs,
                `Agent ${agentId} task execution`
              );
            } else {
              return agent.execute(context);
            }
          },
          {
            label: `Agent ${agentId} task execution`,
            priority: context.metadata?.priority === 'high' ? 'high' : 'medium',
            timeout: timeoutMs
          }
        );
      }

      const executionTime = Date.now() - startTime;

      // Validate result if successful
      if (response.success && response.content) {
        const validation = await this.resultValidator.validateCode(
          response.content,
          this.extractLanguage(context)
        );

        if (!validation.valid) {
          response.suggestions = [
            ...(response.suggestions || []),
            ...validation.suggestions,
            'Validation issues detected - review before implementation'
          ];
        }
      }

      // --- NEW: Handle Micromanager's decomposition output ---
      if (agentId === 'micromanager' && response.success && response.metadata?.decomposition) {
          console.log(`Micromanager execution successful for task "${context.task}", handling decomposition...`);
          await this.handleMicromanagerDecomposition(response, context.metadata?.originalTaskId || context.task, agentId);
      }
      // --- END NEW ---

      // Update metrics and learning
      this.updateAgentStatus(agentId, response, executionTime);
      this.recordTaskCompletion(agentId, context, response, executionTime);

      debugTiming(`Agent ${agentId} task execution`, startTime);
      trackAgentEvent(agentId, 'task_execution_completed', {
        executionTime,
        success: response.success,
        tokensUsed: response.tokensUsed || 0
      });
      trackPerformance('agent_task_execution', executionTime, 'ms', agentId);

      // Notify agent events service of task completion
      this.eventsService.notifyTaskCompleted(
        agentId,
        this.getAgentTypeFromId(agentId),
        `task-${Date.now()}`
      );

      // If this task is linked to a Kanban card, update its status to completed/done
      if (kanbanCardId) {
          // ✅ Check if card is already completed to prevent duplicate moves
          const currentCardState = await this.getCurrentCardState(kanbanCardId);
          if (currentCardState && currentCardState.columnId !== "column-6") {
              console.log(`✅ [TASK COMPLETE] Moving card ${kanbanCardId} (${currentCardState.title}) from ${currentCardState.columnId} to column-6 (Done) for agent ${agentId}`);
              await this.boardService.moveCardToColumn(kanbanCardId, "column-6", agentId).catch(e => debugError(e, `Failed to move card ${kanbanCardId} to done`));
              await this.boardService.updateCardProgress(kanbanCardId, 100, agentId).catch(e => debugError(e, `Failed to update progress for card ${kanbanCardId}`));
          } else if (currentCardState) {
              console.log(`⚠️ [TASK COMPLETE] Card ${kanbanCardId} (${currentCardState.title}) is already in done column (${currentCardState.columnId}), skipping move`);
          } else {
              console.warn(`⚠️ [TASK COMPLETE] Could not get current state for card ${kanbanCardId}, attempting move anyway`);
              await this.boardService.moveCardToColumn(kanbanCardId, "column-6", agentId).catch(e => debugError(e, `Failed to move card ${kanbanCardId} to done`));
          }
      }

      return {
        ...response,
        executionTime
      };

    } catch (error: any) {
      const executionTime = Date.now() - startTime;

      debugError(error, `Agent ${agentId} task execution failed`);
      trackAgentEvent(agentId, 'task_execution_failed', {
        executionTime,
        errorType: error.constructor.name,
        isTimeout: isTimeoutError(error)
      });
      trackError('agent_task_execution_failed', agentId, {
        errorType: error.constructor.name
      });

      status.status = 'error';
      status.currentTask = undefined;
      status.errorCount++;
      status.healthScore = Math.max(0, status.healthScore - 10);

      debugState(`Agent ${agentId}`, 'busy', 'error');

      // Notify agent events service of task failure
      this.eventsService.notifyTaskFailed(
        agentId,
        this.getAgentTypeFromId(agentId),
        `task-${Date.now()}`,
        error instanceof Error ? error.message : String(error)
      );

      // Record failure for learning
      this.recordTaskCompletion(agentId, context, {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      }, executionTime);

      // If this task is linked to a Kanban card, update its status to error
      if (kanbanCardId) {
          // ✅ Check if card is already in backlog to prevent duplicate moves
          const currentCardState = await this.getCurrentCardState(kanbanCardId);
          if (currentCardState && currentCardState.columnId !== "column-1") {
              console.log(`❌ [TASK ERROR] Moving card ${kanbanCardId} (${currentCardState.title}) from ${currentCardState.columnId} to column-1 (Backlog) for agent ${agentId} due to error: ${error instanceof Error ? error.message : String(error)}`);
              await this.boardService.moveCardToColumn(kanbanCardId, "column-1", agentId).catch(e => debugError(e, `Failed to move card ${kanbanCardId} on error`));
              await this.boardService.updateCardProgress(kanbanCardId, 0, agentId).catch(e => debugError(e, `Failed to update progress for card ${kanbanCardId}`));
          } else if (currentCardState) {
              console.log(`⚠️ [TASK ERROR] Card ${kanbanCardId} (${currentCardState.title}) is already in backlog column (${currentCardState.columnId}), skipping move`);
          } else {
              console.warn(`⚠️ [TASK ERROR] Could not get current state for card ${kanbanCardId}, attempting move anyway`);
              await this.boardService.moveCardToColumn(kanbanCardId, "column-1", agentId).catch(e => debugError(e, `Failed to move card ${kanbanCardId} on error`));
          }
      }

      throw error;
    } finally {
        // Ensure agent status is reset to idle even if an error is re-thrown
        const finalStatus = this.agentStatuses.get(agentId);
        if (finalStatus && finalStatus.status !== 'error') { // Keep 'error' status if applicable
            finalStatus.status = 'idle';
            finalStatus.currentTask = undefined;
        }
    }
  }

  /**
   * ✅ Find existing task for a Kanban card to prevent duplicates
   */
  private findExistingTaskForCard(kanbanCardId: string): TaskAssignment | null {
    // Check active tasks
    for (const task of this.activeTasks.values()) {
      if (task.kanbanCardId === kanbanCardId) {
        return task;
      }
    }

    // Check queued tasks
    for (const task of this.taskQueue) {
      if (task.kanbanCardId === kanbanCardId) {
        return task;
      }
    }

    return null;
  }

  /**
   * ✅ Get current card state to prevent duplicate moves
   */
  private async getCurrentCardState(cardId: string): Promise<{ columnId: string; title: string } | null> {
    try {
      // Get board state and find the card
      const boardState = await boardIPCBridge.getBoardState('main');
      if (!boardState) {
        console.warn(`Failed to get board state for card ${cardId}`);
        return null;
      }

      // Find the card across all columns
      for (const column of boardState.columns) {
        const card = column.cards.find(c => c.id === cardId);
        if (card) {
          return {
            columnId: column.id,
            title: card.title
          };
        }
      }

      console.warn(`Card ${cardId} not found in any column`);
      return null;
    } catch (error) {
      console.warn(`Failed to get current state for card ${cardId}:`, error);
      return null;
    }
  }

  /**
   * ✅ Task 93: Get full card data including shell command
   */
  private async getCardData(cardId: string, boardId: string): Promise<any | null> {
    try {
      // Get board state and find the card
      const boardState = await boardIPCBridge.getBoardState(boardId);
      if (!boardState) {
        console.warn(`Failed to get board state for card ${cardId}`);
        return null;
      }

      // Find the card across all columns
      for (const column of boardState.columns) {
        const card = column.cards.find(c => c.id === cardId);
        if (card) {
          return card;
        }
      }

      console.warn(`Card ${cardId} not found in any column`);
      return null;
    } catch (error) {
      console.warn(`Failed to get card data for ${cardId}:`, error);
      return null;
    }
  }

  private extractLanguage(context: AgentContext): string {
    if (context.files) {
      const extensions = context.files.map(file => file.split('.').pop()).filter(Boolean);
      if (extensions.length > 0) {
        return extensions[0] || 'javascript';
      }
    }
    return 'javascript';
  }

  private updateAgentStatus(agentId: string, response: AgentResponse, executionTime: number): void {
    const status = this.agentStatuses.get(agentId);
    if (!status) return;

    // Status is set to idle in finally block of executeTask
    // status.status = 'idle';
    // status.currentTask = undefined;
    status.lastActivity = Date.now();
    const tokensToAdd = isNaN(response.tokensUsed) ? 0 : (response.tokensUsed || 0);
    const currentTokens = isNaN(status.tokensUsed) ? 0 : status.tokensUsed;
    status.tokensUsed = currentTokens + tokensToAdd;

    if (response.success) {
      status.tasksCompleted++;
      const currentHealth = isNaN(status.healthScore) ? 50 : status.healthScore;
      status.healthScore = Math.min(100, currentHealth + 1);
    } else {
      status.errorCount++;
      const currentHealth = isNaN(status.healthScore) ? 50 : status.healthScore;
      status.healthScore = Math.max(0, currentHealth - 5);
    }

    // Update state monitor
    this.stateMonitor.updateAgentMetrics(agentId, {
      tokensUsed: response.tokensUsed,
      responseTime: executionTime,
      success: response.success,
      taskCompleted: true
    });
  }

  private recordTaskCompletion(
    agentId: string,
    context: AgentContext,
    response: AgentResponse,
    executionTime: number
  ): void {
    // Record in learning system
    this.learningAgent.recordTaskCompletion(
      agentId,
      this.extractTaskType(context.task),
      response.success || false,
      executionTime,
      response.tokensUsed || 0,
      context,
      response.content,
      response.error
    );

    // Update system metrics
    this.systemMetrics.totalTasks++;
    this.systemMetrics.totalTokensUsed += response.tokensUsed || 0;

    if (response.success) {
      this.systemMetrics.successfulTasks++;
    } else {
      this.systemMetrics.failedTasks++;
    }

    // Update average response time
    this.systemMetrics.averageResponseTime =
      (this.systemMetrics.averageResponseTime * (this.systemMetrics.totalTasks - 1) + executionTime) /
      this.systemMetrics.totalTasks;
  }

  private extractTaskType(task: string): string {
    const taskLower = task.toLowerCase();
    if (taskLower.includes('implement') || taskLower.includes('create')) return 'implementation';
    if (taskLower.includes('design') || taskLower.includes('ui')) return 'design';
    if (taskLower.includes('test')) return 'testing';
    if (taskLower.includes('research') || taskLower.includes('analyze')) return 'research';
    if (taskLower.includes('fix') || taskLower.includes('debug')) return 'debugging';
    return 'general';
  }

  private getAgentTypeFromId(agentId: string): string {
    // Map agent IDs to their types for presence tracking
    const agentTypeMap: Record<string, string> = {
      'micromanager': 'micromanager',
      'intern': 'intern',
      'junior': 'junior',
      'midlevel': 'midlevel',
      'senior': 'senior',
      'architect': 'architect',
      'designer': 'designer',
      'tester': 'tester',
      'researcher': 'researcher'
    }
    return agentTypeMap[agentId] || agentId
  }

  private async processTaskQueue(): Promise<void> {
    // Sort queue by priority and creation time
    this.taskQueue.sort((a, b) => {
      const priorityOrder = { urgent: 0, high: 1, medium: 2, low: 3 };
      const priorityDiff = priorityOrder[a.priority] - priorityOrder[b.priority];
      return priorityDiff !== 0 ? priorityDiff : a.createdAt - b.createdAt;
    });

    const tasksToProcess = [];

    for (let i = this.taskQueue.length - 1; i >= 0; i--) {
      const task = this.taskQueue[i];
      const agentStatus = this.agentStatuses.get(task.agentId);

      // ✅ Task 81: Use work tracker to check if agent can accept more tasks
      const canAssignTask = this.workTracker.assignTaskToAgent(task.taskId, task.agentId);

      // Only process tasks if agent is idle AND not marked as 'offline' or 'error' AND work tracker allows
      if (agentStatus && agentStatus.status === 'idle' && agentStatus.type !== 'offline' && agentStatus.type !== 'error' && canAssignTask) {
        // Remove from queue and add to processing
        this.taskQueue.splice(i, 1);
        this.activeTasks.set(task.taskId, task);
        tasksToProcess.push(task);
      }
    }

    this.systemMetrics.queueLength = this.taskQueue.length;

    // Execute tasks concurrently
    const processingPromises = tasksToProcess.map(task => this.executeTaskWithRetry(task));
    await Promise.allSettled(processingPromises);
  }

  private async executeTaskWithRetry(task: TaskAssignment): Promise<void> {
    task.status = 'in_progress';
    task.updatedAt = Date.now();

    try {
      // ✅ Budget enforcement - check before task execution
      await this.checkBudgetBeforeExecution(task);

      const response = await this.executeTask(task.agentId, task.context);

      if (response.success) {
        task.status = 'completed';

        // ✅ Task 81: Notify work tracker of successful task completion
        this.workTracker.completeTask(task.taskId, true);

        // ✅ Clean up task from active tasks immediately on completion
        this.activeTasks.delete(task.taskId);
        console.log(`✅ Task ${task.taskId} completed and removed from active tasks`);

        await this.notifyTaskCompletion(task, response);
      } else {
        throw new Error(response.error || 'Task execution failed');
      }

    } catch (error) {
      // ✅ Task 81: Notify work tracker of failed task completion
      this.workTracker.completeTask(task.taskId, false);

      await this.handleTaskError(task, error);
    } finally {
      // task.updatedAt is already set in executeTask and also in finally block
      // by the calling function that handles success/failure.
      // This finally block handles cleanup for this specific `executeTaskWithRetry` call.
      task.updatedAt = Date.now();

      if (task.status === 'completed' || task.status === 'failed') {
        this.activeTasks.delete(task.taskId);
        this.taskHistory.push(task);

        // Keep only last 1000 completed tasks
        if (this.taskHistory.length > 1000) {
          this.taskHistory.shift();
        }
      }
      // Trigger queue processing again after a task finishes (whether success or failure)
      // to pick up next tasks. Use a timeout to prevent stack overflow from immediate re-call.
      setTimeout(() => this.processTaskQueue(), 50);
    }
  }

  /**
   * ✅ Check budget before task execution
   * Throws TaskRejectionError if budget would be exceeded
   */
  private async checkBudgetBeforeExecution(task: TaskAssignment): Promise<void> {
    try {
      // Get cost settings from LLM integration service
      const llmService = require('./llm-integration-service').llmIntegration;
      const settingsManager = llmService.getSettingsManager();
      const settings = settingsManager.getSettings();

      // Only check budget if tracking is enabled
      if (!settings?.cost?.trackUsage || !budgetEnforcer.isBudgetEnforcementEnabled(settings.cost)) {
        return; // Budget enforcement disabled
      }

      // Get agent for token estimation
      const agent = this.agents.get(task.agentId);
      if (!agent) {
        throw new TaskRejectionError(
          TaskRejectedReason.AgentUnavailable,
          task.taskId,
          task.agentId,
          `Agent ${task.agentId} not found`
        );
      }

      // Estimate token usage for this task
      const taskLength = task.context.task.length;
      const contextLength = (task.context.codeContext?.length || 0) +
                           (task.context.rules?.join(' ').length || 0);

      // Conservative estimation: ~4 chars per token
      const estimatedInputTokens = Math.max(500, Math.ceil((taskLength + contextLength) / 4));
      const estimatedOutputTokens = Math.min(4000, Math.max(200, estimatedInputTokens * 0.5));

      // Get agent's provider and model (with fallbacks)
      const provider = agent.config.provider || 'openai';
      const model = agent.config.model || 'gpt-4';

      // Check budget allowance
      const budgetCheck = budgetEnforcer.checkBudget(
        provider,
        model,
        estimatedInputTokens,
        estimatedOutputTokens,
        settings.cost
      );

      if (!budgetCheck.allowed) {
        console.error(`🚫 Task rejected due to budget limit: ${budgetCheck.reason}`);

        // Create detailed budget exceeded error
        const budgetError = new BudgetExceededError(
          provider,
          model,
          budgetCheck.currentCost,
          budgetCheck.estimatedCost,
          budgetCheck.budgetLimit,
          budgetCheck.wouldCost
        );

        // Throw task rejection error with budget details
        throw new TaskRejectionError(
          TaskRejectedReason.BudgetExceeded,
          task.taskId,
          task.agentId,
          `🚫 Task rejected due to exceeded budget limit: $${budgetCheck.currentCost.toFixed(2)} used + $${budgetCheck.estimatedCost.toFixed(4)} estimated = $${budgetCheck.wouldCost.toFixed(2)} would exceed $${budgetCheck.budgetLimit.toFixed(2)} limit`,
          {
            budgetCheck,
            budgetError,
            provider,
            model,
            estimatedTokens: { input: estimatedInputTokens, output: estimatedOutputTokens }
          }
        );
      }

      // Log successful budget check
      console.log(`💰 Budget check passed for task ${task.taskId}: $${budgetCheck.estimatedCost.toFixed(4)} estimated cost, ${budgetCheck.utilizationPercentage.toFixed(1)}% budget used`);

    } catch (error) {
      // Re-throw TaskRejectionError and BudgetExceededError as-is
      if (error instanceof TaskRejectionError || isBudgetExceededError(error)) {
        throw error;
      }

      // Wrap other errors as system errors
      console.warn('Budget check failed due to system error:', error);
      throw new TaskRejectionError(
        TaskRejectedReason.SystemError,
        task.taskId,
        task.agentId,
        `Budget check failed: ${error instanceof Error ? error.message : String(error)}`,
        { originalError: error }
      );
    }
  }

  private async handleTaskError(task: TaskAssignment, error: any): Promise<void> {
    // ✅ Handle budget exceeded errors specially (no retries)
    if (error instanceof TaskRejectionError && error.reason === TaskRejectedReason.BudgetExceeded) {
      task.status = 'failed';
      task.escalationReason = 'Budget limit exceeded - task cannot be retried';

      // Notify budget exceeded failure
      await this.notifyBudgetExceededFailure(task, error);
      return;
    }

    task.retryCount++;

    // Check if we should escalate to error coordinator
    if (task.retryCount >= 2 && task.retryCount < task.maxRetries) {
      try {
        const resolution = await this.errorCoordinator.resolveError(
          error.message || String(error),
          task.agentId,
          [`Attempt ${task.retryCount}: ${error.message}`],
          task.context
        );

        if (resolution.success) {
          // Error resolved, complete the task
          task.status = 'completed';
          await this.notifyTaskCompletion(task, resolution);
          return;
        }
      } catch (coordinatorError) {
        console.warn('Error coordinator failed:', coordinatorError);
      }
    }

    // Standard retry logic
    if (task.retryCount < task.maxRetries) {
      // Try with a different agent if available
      const alternativeAgent = this.selectAlternativeAgent(task.agentId, task.context);
      if (alternativeAgent) {
        task.agentId = alternativeAgent;
        task.escalationReason = `Escalated from ${task.agentId} after ${task.retryCount} attempts`;
      }

      task.status = 'pending';
      this.taskQueue.push(task); // Add back to queue for retry
    } else {
      task.status = 'failed';
      await this.notifyTaskFailure(task, error);
    }
  }

  private selectAlternativeAgent(failedAgentId: string, context: AgentContext): string | null {
    // Escalation hierarchy
    const escalationMap: Record<string, string[]> = {
      'intern': ['junior', 'midlevel'],
      'junior': ['midlevel', 'senior'],
      'midlevel': ['senior'],
      'senior': ['micromanager'], // Last resort: let micromanager decide
      'researcher': ['micromanager'], // Researcher can escalate to micromanager if stuck
      'architect': ['micromanager'],
      'designer': ['micromanager'],
      'tester': ['micromanager'],
    };

    const alternatives = escalationMap[failedAgentId] || [];

    // ✅ Task 81: Use work tracker to find the best available alternative with least load
    for (const agentId of alternatives) {
      const status = this.agentStatuses.get(agentId);
      const workload = this.workTracker.getAgentWorkload(agentId);

      if (status && status.status === 'idle' && status.healthScore > 30 &&
          workload && workload.isAvailable &&
          workload.numberOfActiveTasks < workload.maxConcurrentTasks &&
          workload.healthScore >= 30) {
        return agentId;
      }
    }

    return null;
  }

  private async notifyTaskCompletion(task: TaskAssignment, response: AgentResponse): Promise<void> {
    // ✅ Enhanced debugging for completion message creation
    console.log(`🔍 notifyTaskCompletion: Creating completion message for task ${task.taskId}`)
    console.log(`🔍 Task context metadata:`, task.context.metadata)
    console.log(`🔍 Response metadata:`, response.metadata)
    console.log(`🔍 ChatMessageId from context: "${task.context.metadata?.chatMessageId}"`)

    const message: AgentMessage = {
      agentId: task.agentId,
      taskId: task.taskId,
      type: 'completion',
      message: response.content || `Task completed successfully: ${task.context.task}`,
      timestamp: Date.now(),
      // ✅ Include chat metadata and LLM response metadata for proper streaming
      metadata: {
        chatMessageId: task.context.metadata?.chatMessageId,
        tokensUsed: response.tokensUsed,
        cost: response.cost,
        provider: response.metadata?.provider,
        model: response.metadata?.model,
        finishReason: response.metadata?.finishReason,
        responseTime: response.metadata?.responseTime,
        executionTime: response.executionTime,
        chatInteraction: response.metadata?.chatInteraction
      }
    };

    console.log(`🔍 [📤 Broadcasting LLM Message] Broadcasting completion message:`, {
      agentId: message.agentId,
      taskId: message.taskId,
      type: message.type,
      chatMessageId: message.metadata?.chatMessageId,
      hasContent: !!message.message,
      contentLength: message.message?.length
    })

    this.broadcastMessage(message);

    // ✅ CRITICAL FIX: Add message to BOTH shared state systems for useAgentChatSync polling
    try {
      // Add to agent IPC bridge (shared agent state)
      const { agentIPCBridge } = await import('../../lib/agent-ipc-bridge');
      await agentIPCBridge.addMessage({
        agentId: message.agentId,
        message: message.message || '',
        timestamp: message.timestamp,
        type: message.type,
        metadata: message.metadata
      });
      console.log(`✅ [✅ IPC Received] Message added to agent shared state for chatMessageId: "${message.metadata?.chatMessageId}"`);

      // ✅ ALSO add to global chat state (this is what useAgentChatSync actually polls)
      const { globalChatState } = await import('../../services/global-chat-state');
      const agentChatMessage = {
        id: `agent-completion-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        content: message.message || '',
        role: "agent" as const,
        timestamp: new Date(message.timestamp),
        status: "completed" as const,
        agentType: message.agentId,
        agentId: message.agentId,
        taskId: message.taskId,
        metadata: message.metadata
      };
      await globalChatState.addMessage(agentChatMessage);
      console.log(`✅ [✅ IPC Received] Message ALSO added to global chat state for chatMessageId: "${message.metadata?.chatMessageId}"`);

    } catch (error) {
      console.error('❌ Failed to add completion message to shared state:', error);
    }
  }

  private async notifyTaskFailure(task: TaskAssignment, error: any): Promise<void> {
    const message: AgentMessage = {
      agentId: task.agentId,
      taskId: task.taskId,
      type: 'error',
      message: `Task failed after ${task.retryCount} retries: ${error.message || error}`,
      severity: 'high',
      timestamp: Date.now()
    };

    this.broadcastMessage(message);
  }

  /**
   * ✅ Notify budget exceeded failure with detailed information
   */
  private async notifyBudgetExceededFailure(task: TaskAssignment, error: TaskRejectionError): Promise<void> {
    const budgetDetails = error.details?.budgetCheck;
    const estimatedTokens = error.details?.estimatedTokens;

    const message: AgentMessage = {
      agentId: task.agentId,
      taskId: task.taskId,
      type: 'error',
      message: `🚫 Task rejected due to exceeded budget limit: $${budgetDetails?.currentCost.toFixed(2) || '0.00'} used / $${budgetDetails?.budgetLimit.toFixed(2) || '0.00'} limit`,
      severity: 'high',
      timestamp: Date.now(),
      metadata: {
        rejectionReason: TaskRejectedReason.BudgetExceeded,
        budgetStatus: budgetDetails ? {
          currentCost: budgetDetails.currentCost,
          estimatedCost: budgetDetails.estimatedCost,
          budgetLimit: budgetDetails.budgetLimit,
          utilizationPercentage: budgetDetails.utilizationPercentage,
          remainingBudget: budgetDetails.remainingBudget
        } : undefined,
        estimatedTokens,
        provider: error.details?.provider,
        model: error.details?.model
      }
    };

    this.broadcastMessage(message);

    // Update Kanban card if linked
    const kanbanCardId = task.context.metadata?.kanbanCardId;
    if (kanbanCardId) {
      try {
        // Move card back to backlog with budget exceeded status
        await this.boardService.moveCardToColumn(kanbanCardId, "column-1", task.agentId);

        // Add budget exceeded note to card (if board service supports it)
        console.log(`💸 Kanban card ${kanbanCardId} moved to backlog due to budget limit exceeded`);
      } catch (boardError) {
        console.error('Failed to update Kanban card for budget exceeded task:', boardError);
      }
    }

    // Track budget exceeded event for analytics
    trackAgentEvent(task.agentId, 'task_rejected_budget_exceeded', {
      taskId: task.taskId,
      currentCost: budgetDetails?.currentCost || 0,
      estimatedCost: budgetDetails?.estimatedCost || 0,
      budgetLimit: budgetDetails?.budgetLimit || 0,
      utilizationPercentage: budgetDetails?.utilizationPercentage || 0
    });
  }

  private handleHealthAlert(alert: any): void {
    // Handle health alerts from state monitor
    const message: AgentMessage = {
      agentId: 'state_monitor',
      taskId: 'health_check',
      type: 'error',
      message: `Health Alert - ${alert.agentId}: ${alert.message}`,
      severity: alert.severity === 'red' ? 'high' : 'medium',
      actions: alert.suggestedActions,
      timestamp: Date.now()
    };

    this.broadcastMessage(message);

    // Take automatic actions for critical alerts
    if (alert.severity === 'red') {
      const agentStatus = this.agentStatuses.get(alert.agentId);
      if (agentStatus) {
        agentStatus.status = 'error';
        // Could implement automatic agent restart or model switching here
      }
    }
  }

  private startSystemMonitoring(): void {
    // Update system metrics every 30 seconds
    setInterval(() => {
      this.updateSystemMetrics();
    }, 30000);
  }

  private updateSystemMetrics(): void {
    const allStatuses = Array.from(this.agentStatuses.values());

    this.systemMetrics.activeAgents = allStatuses.filter(s => s.status !== 'offline').length;
    this.systemMetrics.systemHealthScore = allStatuses.length > 0
      ? allStatuses.reduce((sum, s) => sum + (s.healthScore || 0), 0) / allStatuses.length
      : 0;
    this.systemMetrics.queueLength = this.taskQueue.length;
  }

  // --- NEW: Micromanager Decomposition Handling ---
  private async handleMicromanagerDecomposition(
      micromanagerResponse: AgentResponse,
      originalTaskId: string,
      originalAgentId: string // Should be 'micromanager'
  ): Promise<void> {
      if (!micromanagerResponse.success || !micromanagerResponse.metadata?.decomposition) {
          console.error("Micromanager did not return a successful decomposition in metadata.", micromanagerResponse);
          this.broadcastMessage({
            agentId: originalAgentId, taskId: originalTaskId, type: 'error', severity: 'high',
            message: `Micromanager failed to decompose task: No valid decomposition found in response.`
          });
          return;
      }

      const decomposition: TaskDecomposition = micromanagerResponse.metadata.decomposition;
      const createdCardIds: Map<string, string> = new Map(); // Map internal subtask ID to Kanban card ID

      this.broadcastMessage({
        agentId: originalAgentId, taskId: originalTaskId, type: 'update',
        message: `Micromanager decomposed task into ${decomposition.subtasks.length} subtasks.`
      });
      console.log(`Processing decomposition for original task ${originalTaskId}:`, decomposition);

      // Phase 1: Create all subtask cards on the Kanban board
      for (const subtask of decomposition.subtasks) {
          // Map SubTask properties to Kanban Card properties for createTaskCard
          const cardData = {
              title: subtask.description,
              description: `Subtask generated by Micromanager for task: ${originalTaskId}. Type: ${subtask.type}. Complexity: ${subtask.complexity}.`,
              priority: this.mapComplexityToPriority(subtask.complexity), // Map subtask complexity to card priority
              projectId: originalTaskId, // Link to the parent task
              tags: ["micromanager-subtask", subtask.type, subtask.complexity],
              progress: 0,
              swimlaneId: "swimlane-1", // Default for now, could be determined by Micromanager later
              columnId: "column-1", // Default to 'Backlog' (first column)
              storyPoints: Math.ceil(subtask.estimatedTokens / 500) || 1, // Rough estimate, 500 tokens ~ 1 story point
              agentAssignments: [{
                agentId: this.getRecommendedAgentForCard(subtask),
                agentType: "AI", // All system agents are AI
                assignmentTime: new Date().toISOString(),
              }],
              dependencies: [], // Dependencies will be added in Phase 2, after all cards are created
              resourceMetrics: {
                tokenUsage: subtask.estimatedTokens || 0, // Initial estimate from Micromanager
                cpuTime: 0, memoryUsage: 0
              },
              taskHistory: [{
                timestamp: new Date().toISOString(),
                action: "created",
                agentId: originalAgentId,
                details: `Created by Micromanager as subtask "${subtask.id}"`,
              }],
          };

          try {
              // Use the injected boardService to create the card
              const newCard = await this.boardService.createTaskCard(cardData, originalAgentId);
              if (newCard && newCard.id) {
                  createdCardIds.set(subtask.id, newCard.id);
                  console.log(`Created Kanban card for subtask ${subtask.id}: ${newCard.id}`);
                  this.broadcastMessage({
                    agentId: originalAgentId, taskId: originalTaskId, type: 'update',
                    message: `Created Kanban card for subtask: "${subtask.description}" (Card ID: ${newCard.id})`
                  });
              } else {
                  throw new Error("createTaskCard returned null or missing ID.");
              }
          } catch (error: any) {
              console.error(`Failed to create Kanban card for subtask ${subtask.id}:`, error);
              this.broadcastMessage({
                agentId: originalAgentId, taskId: originalTaskId, type: 'error', severity: 'high',
                message: `Failed to create Kanban card for subtask: "${subtask.description}". Error: ${error.message || String(error)}`
              });
          }
      }

      // Phase 2: Add dependencies between newly created cards
      for (const dep of decomposition.dependencies) {
          const sourceCardId = createdCardIds.get(dep.taskId);
          const dependentCardIds = dep.dependsOn.map(id => createdCardIds.get(id)).filter(Boolean); // Filter out null/undefined

          if (sourceCardId && dependentCardIds.length > 0) {
              for (const dependentCardId of dependentCardIds) {
                  try {
                      await this.boardService.addCardDependency(sourceCardId, dependentCardId, originalAgentId);
                      console.log(`Added dependency: Kanban card ${sourceCardId} depends on ${dependentCardId}`);
                      this.broadcastMessage({
                        agentId: originalAgentId, taskId: originalTaskId, type: 'update',
                        message: `Added dependency: Card ${sourceCardId} depends on ${dependentCardId}`
                      });
                  } catch (error: any) {
                      console.error(`Failed to add dependency ${sourceCardId} -> ${dependentCardId}:`, error);
                      this.broadcastMessage({
                        agentId: originalAgentId, taskId: originalTaskId, type: 'error', severity: 'medium',
                        message: `Failed to add dependency: Card ${sourceCardId} depends on ${dependentCardId}. Error: ${error.message || String(error)}`
                      });
                  }
              }
          }
      }

      this.broadcastMessage({
        agentId: originalAgentId, taskId: originalTaskId, type: 'completion',
        message: `Task decomposition complete. Kanban board updated with subtasks and dependencies.`
      });

      // After decomposition and card creation, assign these new tasks to the appropriate agents
      // This would involve adding these new tasks to the AgentManager's task queue (this.taskQueue)
      // For now, the cards are created. The AgentManager's processTaskQueue will pick them up later.
      // This could be optimized to directly assign the next ready task.
  }

  private mapComplexityToPriority(complexity: SubTask['complexity']): string {
      switch (complexity) {
          case 'trivial': return 'low';
          case 'simple': return 'low';
          case 'moderate': return 'medium';
          case 'complex': return 'high';
          default: return 'medium'; // Fallback
      }
  }

  private getRecommendedAgentForCard(subtask: SubTask): string {
      // This logic is a replication of MicromanagerAgent's internal recommendation.
      // Ideally, Micromanager would provide the final agent ID in its decomposition directly.
      switch (subtask.type) {
        case 'research': return 'researcher';
        case 'architecture': return 'architect';
        case 'design': return 'designer';
        case 'testing': return 'tester';
        case 'code_generation':
        case 'code_modification':
          switch (subtask.complexity) {
            case 'trivial': return 'intern';
            case 'simple': return 'junior';
            case 'moderate': return 'midlevel';
            case 'complex': return 'senior';
            default: return 'junior';
          }
        default: return 'junior';
      }
  }
  // --- END NEW ---

  // Public API methods
  public getSystemMetrics(): SystemMetrics {
    return { ...this.systemMetrics };
  }

  public getAgents(): AgentBase[] {
    return Array.from(this.agents.values());
  }

  public getAgent(id: string): AgentBase | null {
    return this.agents.get(id) || null;
  }

  public getAgentStatus(id: string): AgentStatus | null {
    return this.agentStatuses.get(id) || null;
  }

  public getAllAgentStatuses(): AgentStatus[] {
    return Array.from(this.agentStatuses.values());
  }

  public getActiveTasks(): TaskAssignment[] {
    return Array.from(this.activeTasks.values());
  }

  public getQueuedTasks(): TaskAssignment[] {
    return [...this.taskQueue];
  }

  public getTaskHistory(limit = 50): TaskAssignment[] {
    return this.taskHistory.slice(-limit);
  }

  public getTaskById(taskId: string): TaskAssignment | null {
    return this.activeTasks.get(taskId) ||
           this.taskQueue.find(t => t.taskId === taskId) ||
           this.taskHistory.find(t => t.taskId === taskId) ||
           null;
  }

  public cancelTask(taskId: string): boolean {
    // Remove from queue if pending
    const queueIndex = this.taskQueue.findIndex(t => t.taskId === taskId);
    if (queueIndex > -1) {
      this.taskQueue.splice(queueIndex, 1);
      this.systemMetrics.queueLength = this.taskQueue.length;
      return true;
    }

    // Cancel active task
    if (this.activeTasks.has(taskId)) {
      const task = this.activeTasks.get(taskId)!;
      task.status = 'failed';
      task.updatedAt = Date.now();
      this.activeTasks.delete(taskId);
      this.taskHistory.push(task);
      return true;
    }

    return false;
  }

  // Learning and optimization methods
  public getOptimizationSuggestions(agentId?: string): any[] {
    return this.learningAgent.getOptimizationSuggestions(agentId);
  }

  public getLearningPatterns(category?: string): any[] {
    return this.learningAgent.getLearningPatterns(category);
  }

  public getPerformanceMetrics(agentId?: string): any[] {
    return this.learningAgent.getPerformanceMetrics(agentId);
  }

  public async generateSystemReport(): Promise<string> {
    const metrics = this.getSystemMetrics();
    const agentStatuses = this.getAllAgentStatuses();
    const optimizations = this.getOptimizationSuggestions();

    return `[SYSTEM STATUS REPORT]

OVERVIEW:
- System Health: ${metrics.systemHealthScore.toFixed(1)}%
- Active Agents: ${metrics.activeAgents}/${agentStatuses.length}
- Queue Length: ${metrics.queueLength}
- Total Tasks: ${metrics.totalTasks}
- Success Rate: ${((metrics.successfulTasks / Math.max(metrics.totalTasks, 1)) * 100).toFixed(1)}%

AGENT STATUS:
${agentStatuses.map(agent => {
  const health = agent.healthScore;
  const status = health > 70 ? '🟢' : health > 40 ? '🟡' : '🔴';
  return `${status} ${agent.name}: ${health.toFixed(1)}% (${agent.tasksCompleted} tasks)`;
}).join('\n')}

PERFORMANCE METRICS:
- Average Response Time: ${(metrics.averageResponseTime / 1000).toFixed(2)}s
- Total Tokens Used: ${metrics.totalTokensUsed.toLocaleString()}
- Failed Tasks: ${metrics.failedTasks}

OPTIMIZATION OPPORTUNITIES:
${optimizations.slice(0, 5).map(opt => `- ${opt.description}`).join('\n')}

ACTIVE TASKS: ${this.getActiveTasks().length}
QUEUED TASKS: ${this.getQueuedTasks().length}

[END REPORT]`;
  }

  // Event handling
  public onMessage(listener: (message: AgentMessage) => void): void {
    this.messageListeners.push(listener);
  }

  public offMessage(listener: (message: AgentMessage) => void): void {
    const index = this.messageListeners.indexOf(listener);
    if (index > -1) {
      this.messageListeners.splice(index, 1);
    }
  }

  private broadcastMessage(message: AgentMessage): void {
    this.messageListeners.forEach(listener => listener(message));
  }

  /**
   * Show user-visible error message
   */
  private showUserError(message: string): void {
    try {
      // Try to show toast notification if available
      if (typeof window !== 'undefined' && (window as any).showToast) {
        (window as any).showToast({
          title: "Agent Error",
          description: message,
          variant: "destructive",
        });
      } else {
        // Fallback to console error
        console.error('Agent Error:', message);

        // Try to show alert as last resort
        if (typeof window !== 'undefined' && window.alert) {
          window.alert(`Agent Error: ${message}`);
        }
      }
    } catch (error) {
      console.error('Failed to show user error:', error);
    }
  }

  // Agent file system operations with cross-window sync
  public async agentCreateFile(agentId: string, path: string, content: string = ''): Promise<boolean> {
    try {
      // Enforce project context requirement
      const { activeProjectService } = await import('../services/active-project-service');
      if (!activeProjectService.getActiveProjectPath()) {
        const errorMessage = "Agent execution blocked: no active project selected. Please open or create a project first.";
        console.error(`Agent ${agentId}: ${errorMessage}`);

        // Show user-visible error
        this.showUserError(errorMessage);
        throw new Error(errorMessage);
      }

      // Resolve path using active project service if it's a relative path
      let resolvedPath = path;
      if (!path.startsWith('/')) {
        try {
          resolvedPath = activeProjectService.resolve(path);
          console.log(`Agent ${agentId}: Resolved relative path '${path}' to '${resolvedPath}'`);
        } catch (error) {
          console.error(`Agent ${agentId}: Failed to resolve path '${path}':`, error);
          throw new Error(`Cannot resolve file path '${path}': ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
      }

      const result = await this.fileOperations.createFile(resolvedPath, content, {
        backup: true,
        overwrite: false
      }, agentId);

      if (result.success) {
        // Broadcast agent action via IPC
        await agentIPCBridge.addMessage({
          agentId,
          message: `Created file: ${resolvedPath}`,
          timestamp: Date.now(),
          type: 'info'
        });

        // Trigger file explorer refresh across all windows
        if (typeof window !== 'undefined' && window.electronAPI && window.electronAPI.ipc) {
          window.electronAPI.ipc.send('file-system-changed', { type: 'create', path: resolvedPath });
        }

        return true;
      }
      return false;
    } catch (error) {
      console.error(`Agent ${agentId} failed to create file ${path}:`, error);
      return false;
    }
  }

  public async agentWriteFile(agentId: string, path: string, content: string): Promise<boolean> {
    try {
      const result = await this.fileOperations.writeFile(path, content, {
        backup: true
      }, agentId);

      if (result.success) {
        // Broadcast agent action via IPC
        await agentIPCBridge.addMessage({
          agentId,
          message: `Modified file: ${path}`,
          timestamp: Date.now(),
          type: 'info'
        });

        // Trigger file explorer refresh across all windows
        if (typeof window !== 'undefined' && window.electronAPI && window.electronAPI.ipc) {
          window.electronAPI.ipc.send('file-system-changed', { type: 'modify', path });
        }

        return true;
      }
      return false;
    } catch (error) {
      console.error(`Agent ${agentId} failed to write file ${path}:`, error);
      return false;
    }
  }

  public async agentDeleteFile(agentId: string, path: string): Promise<boolean> {
    try {
      const result = await this.fileOperations.deleteFile(path, {
        backup: true
      }, agentId);

      if (result.success) {
        // Broadcast agent action via IPC
        await agentIPCBridge.addMessage({
          agentId,
          message: `Deleted file: ${path}`,
          timestamp: Date.now(),
          type: 'info'
        });

        // Trigger file explorer refresh across all windows
        if (typeof window !== 'undefined' && window.electronAPI && window.electronAPI.ipc) {
          window.electronAPI.ipc.send('file-system-changed', { type: 'delete', path });
        }

        return true;
      }
      return false;
    } catch (error) {
      console.error(`Agent ${agentId} failed to delete file ${path}:`, error);
      return false;
    }
  }

  public async agentCreateDirectory(agentId: string, path: string): Promise<boolean> {
    try {
      // Enforce project context requirement
      const { activeProjectService } = await import('../services/active-project-service');
      if (!activeProjectService.getActiveProjectPath()) {
        const errorMessage = "Agent execution blocked: no active project selected. Please open or create a project first.";
        console.error(`Agent ${agentId}: ${errorMessage}`);

        // Show user-visible error
        this.showUserError(errorMessage);
        throw new Error(errorMessage);
      }

      // Resolve path using active project service if it's a relative path
      let resolvedPath = path;
      if (!path.startsWith('/')) {
        try {
          resolvedPath = activeProjectService.resolve(path);
          console.log(`Agent ${agentId}: Resolved relative directory path '${path}' to '${resolvedPath}'`);
        } catch (error) {
          console.error(`Agent ${agentId}: Failed to resolve directory path '${path}':`, error);
          throw new Error(`Cannot resolve directory path '${path}': ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
      }

      const result = await this.fileOperations.createDirectory(resolvedPath, {}, agentId);

      if (result.success) {
        // Broadcast agent action via IPC
        await agentIPCBridge.addMessage({
          agentId,
          message: `Created directory: ${resolvedPath}`,
          timestamp: Date.now(),
          type: 'info'
        });

        // Trigger file explorer refresh across all windows
        if (typeof window !== 'undefined' && window.electronAPI && window.electronAPI.ipc) {
          window.electronAPI.ipc.send('file-system-changed', { type: 'create', path: resolvedPath });
        }

        return true;
      }
      return false;
    } catch (error) {
      console.error(`Agent ${agentId} failed to create directory ${path}:`, error);
      return false;
    }
  }

  // Agent terminal operations with cross-window sync
  public async agentExecuteCommand(agentId: string, command: string, cwd?: string): Promise<{ success: boolean; output?: string; error?: string }> {
    try {
      // Broadcast command execution start
      await agentIPCBridge.addMessage({
        agentId,
        message: `Executing command: ${command}`,
        timestamp: Date.now(),
        type: 'info'
      });

      // Execute command via Electron API if available
      if (typeof window !== 'undefined' && window.electronAPI && window.electronAPI.executeCommand) {
        const result = await window.electronAPI.executeCommand(command, cwd);

        // Broadcast command result
        await agentIPCBridge.addMessage({
          agentId,
          message: `Command completed: ${command} (${result.success ? 'success' : 'failed'})`,
          timestamp: Date.now(),
          type: result.success ? 'success' : 'error'
        });



        return result;
      } else {
        // Fallback for non-Electron environments
        console.log(`Agent ${agentId} would execute: ${command}`);
        return { success: true, output: `Simulated execution of: ${command}` };
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);

      // Broadcast command error
      await agentIPCBridge.addMessage({
        agentId,
        message: `Command failed: ${command} - ${errorMessage}`,
        timestamp: Date.now(),
        type: 'error'
      });

      return { success: false, error: errorMessage };
    }
  }

  // ✅ Task 81: Public API methods for load monitoring and balancing
  public getLoadBalancingMetrics() {
    return this.workTracker.getLoadBalancingMetrics();
  }

  public getAgentWorkloads() {
    return this.workTracker.getAllAgentWorkloads();
  }

  public getOverloadedAgents() {
    return this.workTracker.getOverloadedAgents();
  }

  public assignAgentWithLeastLoad(role: string): string | null {
    return this.workTracker.assignAgentWithLeastLoad(role);
  }

  public setAgentAvailability(agentId: string, isAvailable: boolean): void {
    this.workTracker.setAgentAvailability(agentId, isAvailable);
  }

  // ✅ Agent health management methods
  public resetAgentHealth(agentId: string): void {
    this.workTracker.resetAgentHealth(agentId);
  }

  public resetAllAgentHealth(): void {
    this.workTracker.resetAllAgentHealth();
  }

  public updateAgentHealthScore(agentId: string, healthScore: number): void {
    this.workTracker.updateAgentHealthScore(agentId, healthScore);
  }

  // Cleanup
  public async shutdown(): Promise<void> {
    // Cancel all pending tasks
    this.taskQueue.forEach(task => {
      task.status = 'failed';
      task.updatedAt = Date.now();
    });

    // Clear active tasks
    this.activeTasks.clear();

    // Stop monitoring
    if (this.stateMonitor) {
      this.stateMonitor.destroy();
    }

    console.log('Agent Manager shutdown complete');
  }
}